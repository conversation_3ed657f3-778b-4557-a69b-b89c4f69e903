#ifndef DATABASE_SQL_COLUMN_H
#define DATABASE_SQL_COLUMN_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <optional>

#include "sql_object.h"
#include "../sql_enums.h"
#include "../types/variant.h"

namespace database {

// Forward declarations
class SqlTable;

/**
 * @brief SQL Column abstraction with dual-purpose design
 *
 * This class supports two modes of operation:
 * 1. Definition Mode: Building column definitions for new tables
 * 2. Access Mode: Accessing existing column metadata from database
 *
 * The class provides a fluent interface for column definition and
 * automatic synchronization with database metadata.
 */
class SqlColumn : public SqlObject {
public:
    /**
     * @brief Column constraint information
     */
    struct Constraint {
        SqlConstraintType type;
        std::string name;
        Variant value;
        std::string referencedTable;
        std::string referencedColumn;

        Constraint(SqlConstraintType t, std::string_view n = "", const Variant& v = Variant())
            : type(t), name(n), value(v) {}
    };

    //----------------------------------------------------------------------
    // Constructors (Definition Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Default constructor (Definition Mode)
     */
    SqlColumn();

    /**
     * @brief Constructor with name (Definition Mode)
     * @param name The column name
     */
    explicit SqlColumn(std::string_view name);

    /**
     * @brief Constructor with name and type (Definition Mode)
     * @param name The column name
     * @param type The column data type
     */
    SqlColumn(std::string_view name, FieldType type);

    /**
     * @brief Constructor with name, type and size (Definition Mode)
     * @param name The column name
     * @param type The column data type
     * @param size The column size/length
     */
    SqlColumn(std::string_view name, FieldType type, int size);

    /**
     * @brief Constructor for Access Mode
     * @param name The column name
     * @param table The parent table
     * @param driver The database driver
     */
    SqlColumn(std::string_view name, SqlTable* table, SqlDriver* driver);

    //----------------------------------------------------------------------
    // Static Factory Methods (Access Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Create column from database metadata
     * @param name The column name
     * @param table The parent table
     * @param driver The database driver
     * @return SqlColumn instance loaded from database
     */
    [[nodiscard]] static SqlColumn fromDatabase(std::string_view name, SqlTable* table, SqlDriver* driver);

    /**
     * @brief Create column from SQL field metadata
     * @param field The SQL field containing metadata
     * @param table The parent table
     * @param driver The database driver
     * @return SqlColumn instance
     */
    [[nodiscard]] static SqlColumn fromField(const class SqlField& field, SqlTable* table, SqlDriver* driver);

    //----------------------------------------------------------------------
    // Column Properties
    //----------------------------------------------------------------------

    /**
     * @brief Get the column data type
     * @return The field type
     */
    [[nodiscard]] FieldType dataType() const noexcept;

    /**
     * @brief Set the column data type
     * @param type The field type
     * @return Reference to this column for method chaining
     */
    SqlColumn& setDataType(FieldType type);

    /**
     * @brief Get the column size/length
     * @return The column size, or -1 if not applicable
     */
    [[nodiscard]] int size() const noexcept;

    /**
     * @brief Set the column size/length
     * @param size The column size
     * @return Reference to this column for method chaining
     */
    SqlColumn& setSize(int size);

    /**
     * @brief Get the column precision (for decimal types)
     * @return The precision, or -1 if not applicable
     */
    [[nodiscard]] int precision() const noexcept;

    /**
     * @brief Set the column precision
     * @param precision The precision
     * @return Reference to this column for method chaining
     */
    SqlColumn& setPrecision(int precision);

    /**
     * @brief Get the column scale (for decimal types)
     * @return The scale, or -1 if not applicable
     */
    [[nodiscard]] int scale() const noexcept;

    /**
     * @brief Set the column scale
     * @param scale The scale
     * @return Reference to this column for method chaining
     */
    SqlColumn& setScale(int scale);

    /**
     * @brief Check if the column allows NULL values
     * @return True if nullable, false otherwise
     */
    [[nodiscard]] bool isNullable() const noexcept;

    /**
     * @brief Set the column nullable property
     * @param nullable Whether the column allows NULL values
     * @return Reference to this column for method chaining
     */
    SqlColumn& setNullable(bool nullable);

    /**
     * @brief Get the default value
     * @return The default value, or empty Variant if none
     */
    [[nodiscard]] const Variant& defaultValue() const noexcept;

    /**
     * @brief Set the default value
     * @param value The default value
     * @return Reference to this column for method chaining
     */
    SqlColumn& setDefaultValue(const Variant& value);

    /**
     * @brief Check if the column is auto-increment
     * @return True if auto-increment, false otherwise
     */
    [[nodiscard]] bool isAutoIncrement() const noexcept;

    /**
     * @brief Set the auto-increment property
     * @param autoIncrement Whether the column is auto-increment
     * @return Reference to this column for method chaining
     */
    SqlColumn& setAutoIncrement(bool autoIncrement);

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------

    /**
     * @brief Get the parent table
     * @return Pointer to the parent table, or nullptr if not set
     */
    [[nodiscard]] SqlTable* table() const noexcept;

    /**
     * @brief Set the parent table
     * @param table The parent table
     */
    void setTable(SqlTable* table);

    /**
     * @brief Get the table name
     * @return The table name
     */
    [[nodiscard]] std::string tableName() const;

    /**
     * @brief Set the table name
     * @param tableName The table name
     */
    void setTableName(std::string_view tableName);

    //----------------------------------------------------------------------
    // Constraints
    //----------------------------------------------------------------------

    /**
     * @brief Add a constraint to the column
     * @param constraint The constraint to add
     * @return Reference to this column for method chaining
     */
    SqlColumn& addConstraint(const Constraint& constraint);

    /**
     * @brief Remove a constraint by type
     * @param type The constraint type to remove
     * @return Reference to this column for method chaining
     */
    SqlColumn& removeConstraint(SqlConstraintType type);

    /**
     * @brief Get all constraints
     * @return Vector of constraints
     */
    [[nodiscard]] const std::vector<Constraint>& constraints() const noexcept;

    /**
     * @brief Check if column has a specific constraint type
     * @param type The constraint type to check
     * @return True if constraint exists, false otherwise
     */
    [[nodiscard]] bool hasConstraint(SqlConstraintType type) const noexcept;

    /**
     * @brief Get constraint by type
     * @param type The constraint type
     * @return Optional constraint, empty if not found
     */
    [[nodiscard]] std::optional<Constraint> getConstraint(SqlConstraintType type) const;

    //----------------------------------------------------------------------
    // Fluent Interface for Definition Mode
    //----------------------------------------------------------------------

    /**
     * @brief Set column as primary key
     * @return Reference to this column for method chaining
     */
    SqlColumn& primaryKey();

    /**
     * @brief Set column as unique
     * @return Reference to this column for method chaining
     */
    SqlColumn& unique();

    /**
     * @brief Set column as not null
     * @return Reference to this column for method chaining
     */
    SqlColumn& notNull();

    /**
     * @brief Set column as auto increment
     * @return Reference to this column for method chaining
     */
    SqlColumn& autoIncrement();

    /**
     * @brief Set default value
     * @param value The default value
     * @return Reference to this column for method chaining
     */
    SqlColumn& defaultValue(const Variant& value);

    /**
     * @brief Add foreign key constraint
     * @param referencedTable The referenced table name
     * @param referencedColumn The referenced column name
     * @return Reference to this column for method chaining
     */
    SqlColumn& foreignKey(std::string_view referencedTable, std::string_view referencedColumn);

    /**
     * @brief Add check constraint
     * @param condition The check condition
     * @return Reference to this column for method chaining
     */
    SqlColumn& check(std::string_view condition);

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------

    /**
     * @brief Get the object type
     * @return SqlObjectType::Table (columns are part of tables)
     */
    [[nodiscard]] SqlObjectType objectType() const override;

    /**
     * @brief Check if column exists in database
     * @return True if column exists in the parent table
     */
    [[nodiscard]] bool existsInDatabase() const override;

    /**
     * @brief Load column definition from database
     * @return True if successful, false otherwise
     */
    bool loadFromDatabase() override;

    /**
     * @brief Generate CREATE SQL for column (as part of CREATE TABLE)
     * @return The column definition SQL
     */
    [[nodiscard]] std::string generateCreateSql() const override;

    /**
     * @brief Generate ALTER SQL for column modifications
     * @return The ALTER COLUMN SQL
     */
    [[nodiscard]] std::string generateAlterSql() const override;

    //----------------------------------------------------------------------
    // Utility Methods
    //----------------------------------------------------------------------

    /**
     * @brief Get the SQL type string for the column
     * @return The SQL type string (e.g., "VARCHAR(255)", "INTEGER")
     */
    [[nodiscard]] std::string sqlTypeString() const;

    /**
     * @brief Get the qualified column name (table.column)
     * @return The qualified column name
     */
    [[nodiscard]] std::string qualifiedColumnName() const;

private:
    // Column properties
    FieldType m_dataType{FieldType::Unknown};
    int m_size{-1};
    int m_precision{-1};
    int m_scale{-1};
    bool m_nullable{true};
    bool m_autoIncrement{false};
    Variant m_defaultValue;

    // Table association
    SqlTable* m_table{nullptr};
    std::string m_tableName;

    // Constraints
    std::vector<Constraint> m_constraints;

    // Helper methods
    void loadMetadataFromDatabase();
    std::string generateConstraintSql() const;
    std::string fieldTypeToSqlString(FieldType type) const;
};

// Type aliases for compatibility with existing code
using Column = SqlColumn;

} // namespace database

#endif // DATABASE_SQL_COLUMN_H
