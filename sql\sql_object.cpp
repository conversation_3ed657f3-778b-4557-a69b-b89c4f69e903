#include "sql_object.h"

#include <format>
#include <algorithm>

#include "../driver/sql_driver.h"
#include "../driver/sql_statement.h"

namespace database {

SqlObject::SqlObject()
    : m_syncState(SqlSyncState::LocalOnly) {
}

SqlObject::SqlObject(std::string_view name)
    : m_name(name), m_syncState(SqlSyncState::LocalOnly) {
}

SqlObject::SqlObject(std::string_view name, SqlDriver* driver)
    : m_name(name), m_driver(driver), m_syncState(SqlSyncState::DatabaseOnly) {
}

SqlObject::SqlObject(const SqlObject& other) {
    std::shared_lock lock(other.m_stateMutex);
    m_name = other.m_name;
    m_schema = other.m_schema;
    m_syncState.store(other.m_syncState.load());
    m_hasLocalChanges.store(other.m_hasLocalChanges.load());
    m_driver = other.m_driver;

    std::lock_guard errorLock(other.m_errorMutex);
    m_lastError = other.m_lastError;
}

SqlObject::SqlObject(SqlObject&& other) noexcept {
    std::unique_lock lock(other.m_stateMutex);
    m_name = std::move(other.m_name);
    m_schema = std::move(other.m_schema);
    m_syncState.store(other.m_syncState.load());
    m_hasLocalChanges.store(other.m_hasLocalChanges.load());
    m_driver = other.m_driver;
    other.m_driver = nullptr;

    std::lock_guard errorLock(other.m_errorMutex);
    m_lastError = std::move(other.m_lastError);
}

SqlObject& SqlObject::operator=(const SqlObject& other) {
    if (this != &other) {
        std::unique_lock lock1(m_stateMutex, std::defer_lock);
        std::shared_lock lock2(other.m_stateMutex, std::defer_lock);
        std::lock(lock1, lock2);

        m_name = other.m_name;
        m_schema = other.m_schema;
        m_syncState.store(other.m_syncState.load());
        m_hasLocalChanges.store(other.m_hasLocalChanges.load());
        m_driver = other.m_driver;

        std::lock_guard errorLock(other.m_errorMutex);
        m_lastError = other.m_lastError;
    }
    return *this;
}

SqlObject& SqlObject::operator=(SqlObject&& other) noexcept {
    if (this != &other) {
        std::unique_lock lock1(m_stateMutex, std::defer_lock);
        std::unique_lock lock2(other.m_stateMutex, std::defer_lock);
        std::lock(lock1, lock2);

        m_name = std::move(other.m_name);
        m_schema = std::move(other.m_schema);
        m_syncState.store(other.m_syncState.load());
        m_hasLocalChanges.store(other.m_hasLocalChanges.load());
        m_driver = other.m_driver;
        other.m_driver = nullptr;

        std::lock_guard errorLock(other.m_errorMutex);
        m_lastError = std::move(other.m_lastError);
    }
    return *this;
}

std::string_view SqlObject::name() const noexcept {
    std::shared_lock lock(m_stateMutex);
    return m_name;
}

void SqlObject::setName(std::string_view name) {
    std::unique_lock lock(m_stateMutex);
    if (m_name != name) {
        m_name = name;
        markAsModified();
    }
}

std::string_view SqlObject::schema() const noexcept {
    std::shared_lock lock(m_stateMutex);
    return m_schema;
}

void SqlObject::setSchema(std::string_view schema) {
    std::unique_lock lock(m_stateMutex);
    if (m_schema != schema) {
        m_schema = schema;
        markAsModified();
    }
}

std::string SqlObject::qualifiedName() const {
    std::shared_lock lock(m_stateMutex);
    if (m_schema.empty()) {
        return m_name;
    }
    return std::format("{}.{}", m_schema, m_name);
}

SqlSyncState SqlObject::syncState() const noexcept {
    return m_syncState.load();
}

bool SqlObject::isDefinitionMode() const noexcept {
    auto state = m_syncState.load();
    return state == SqlSyncState::LocalOnly || state == SqlSyncState::Unsynced;
}

bool SqlObject::isAccessMode() const noexcept {
    auto state = m_syncState.load();
    return state == SqlSyncState::DatabaseOnly || state == SqlSyncState::Synced;
}

bool SqlObject::existsInDatabase() const {
    if (!m_driver) {
        setError("No database driver available", ErrorCode::ConnectionClosed);
        return false;
    }

    // This is a basic implementation - derived classes should override
    // with object-specific existence checks
    return m_driver->isConnected();
}

bool SqlObject::hasLocalChanges() const noexcept {
    return m_hasLocalChanges.load();
}

SqlDriver* SqlObject::driver() const noexcept {
    return m_driver;
}

void SqlObject::setDriver(SqlDriver* driver) {
    std::unique_lock lock(m_stateMutex);
    m_driver = driver;

    if (driver) {
        auto currentState = m_syncState.load();
        if (currentState == SqlSyncState::LocalOnly) {
            setSyncState(SqlSyncState::Unsynced);
        }
    }
}

bool SqlObject::createInDatabase(SqlConflictResolution conflictResolution) {
    if (!m_driver) {
        setError("No database driver available", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!isDefinitionMode()) {
        setError("Object is not in definition mode", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if object already exists
    if (existsInDatabase()) {
        switch (conflictResolution) {
            case SqlConflictResolution::PreferLocal:
                // Drop existing and recreate
                if (!dropFromDatabase()) {
                    return false;
                }
                break;
            case SqlConflictResolution::PreferDatabase:
                // Load from database instead
                return loadFromDatabase();
            case SqlConflictResolution::Manual:
                setError("Object already exists in database", ErrorCode::ConstraintViolation);
                return false;
            case SqlConflictResolution::Merge:
                // Attempt to merge - for now, prefer local
                if (!dropFromDatabase()) {
                    return false;
                }
                break;
        }
    }

    // Generate and execute CREATE SQL
    std::string createSql = generateCreateSql();
    if (createSql.empty()) {
        setError("Failed to generate CREATE SQL", ErrorCode::InternalError);
        return false;
    }

    if (!executeSql(createSql)) {
        return false;
    }

    // Update synchronization state
    setSyncState(SqlSyncState::Synced);
    clearModified();
    clearError();

    return true;
}

bool SqlObject::loadFromDatabase() {
    if (!m_driver) {
        setError("No database driver available", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!existsInDatabase()) {
        setError("Object does not exist in database", ErrorCode::ExecutionFailed);
        return false;
    }

    // Derived classes should implement specific loading logic
    setSyncState(SqlSyncState::Synced);
    clearModified();
    clearError();

    return true;
}

bool SqlObject::synchronize(SqlConflictResolution conflictResolution) {
    if (!m_driver) {
        setError("No database driver available", ErrorCode::ConnectionClosed);
        return false;
    }

    auto currentState = m_syncState.load();

    switch (currentState) {
        case SqlSyncState::LocalOnly:
            return createInDatabase(conflictResolution);
        case SqlSyncState::Unsynced:
            return createInDatabase(conflictResolution);
        case SqlSyncState::DatabaseOnly:
            return loadFromDatabase();
        case SqlSyncState::Synced:
            if (hasLocalChanges()) {
                // Apply local changes to database
                std::string alterSql = generateAlterSql();
                if (!alterSql.empty() && !executeSql(alterSql)) {
                    return false;
                }
                clearModified();
            }
            return true;
        case SqlSyncState::Conflicted:
            // Handle conflict based on resolution strategy
            switch (conflictResolution) {
                case SqlConflictResolution::PreferLocal:
                    return createInDatabase(conflictResolution);
                case SqlConflictResolution::PreferDatabase:
                    return loadFromDatabase();
                case SqlConflictResolution::Manual:
                    setError("Manual conflict resolution required", ErrorCode::ConstraintViolation);
                    return false;
                case SqlConflictResolution::Merge:
                    // Implement merge logic in derived classes
                    return loadFromDatabase();
            }
            break;
    }

    return false;
}

bool SqlObject::dropFromDatabase() {
    if (!m_driver) {
        setError("No database driver available", ErrorCode::ConnectionClosed);
        return false;
    }

    std::string dropSql = generateDropSql();
    if (dropSql.empty()) {
        setError("Failed to generate DROP SQL", ErrorCode::InternalError);
        return false;
    }

    if (!executeSql(dropSql)) {
        return false;
    }

    setSyncState(SqlSyncState::LocalOnly);
    clearError();

    return true;
}

std::string SqlObject::generateDropSql() const {
    return std::format("DROP {} {}",
        objectType() == SqlObjectType::Table ? "TABLE" : "OBJECT",
        qualifiedName());
}

std::string SqlObject::generateAlterSql() const {
    // Base implementation - derived classes should override
    return "";
}

const SqlError& SqlObject::lastError() const noexcept {
    std::lock_guard lock(m_errorMutex);
    return m_lastError;
}

void SqlObject::clearError() noexcept {
    std::lock_guard lock(m_errorMutex);
    m_lastError.clear();
}

size_t SqlObject::addSyncCallback(SyncCallback callback) {
    std::lock_guard lock(m_callbackMutex);
    size_t id = m_nextCallbackId.fetch_add(1);
    m_syncCallbacks[id] = std::move(callback);
    return id;
}

void SqlObject::removeSyncCallback(size_t callbackId) {
    std::lock_guard lock(m_callbackMutex);
    m_syncCallbacks.erase(callbackId);
}

void SqlObject::setSyncState(SqlSyncState newState) {
    auto oldState = m_syncState.exchange(newState);
    if (oldState != newState) {
        notifySyncStateChange(oldState, newState);
    }
}

void SqlObject::setError(const SqlError& error) noexcept {
    std::lock_guard lock(m_errorMutex);
    m_lastError = error;
}

void SqlObject::setError(std::string_view message, ErrorCode code, std::string_view sqlState) {
    std::lock_guard lock(m_errorMutex);
    m_lastError = SqlError(message, code, sqlState);
}

bool SqlObject::executeSql(std::string_view sql) const {
    if (!m_driver || !m_driver->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    auto stmt = m_driver->createStatement();
    if (!stmt) {
        setError("Failed to create statement", ErrorCode::InternalError);
        return false;
    }

    if (!stmt->prepare(sql)) {
        setError("Failed to prepare statement", ErrorCode::StatementInvalid);
        return false;
    }

    if (!stmt->execute()) {
        setError("Failed to execute statement", ErrorCode::ExecutionFailed);
        return false;
    }

    return true;
}

std::shared_ptr<SqlStatement> SqlObject::prepareStatement(std::string_view sql) const {
    if (!m_driver || !m_driver->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return nullptr;
    }

    auto stmt = m_driver->createStatement();
    if (!stmt) {
        setError("Failed to create statement", ErrorCode::InternalError);
        return nullptr;
    }

    if (!stmt->prepare(sql)) {
        setError("Failed to prepare statement", ErrorCode::StatementInvalid);
        return nullptr;
    }

    return stmt;
}

void SqlObject::markAsModified() {
    m_hasLocalChanges.store(true);

    auto currentState = m_syncState.load();
    if (currentState == SqlSyncState::Synced) {
        setSyncState(SqlSyncState::Unsynced);
    }
}

void SqlObject::clearModified() {
    m_hasLocalChanges.store(false);
}

void SqlObject::notifySyncStateChange(SqlSyncState oldState, SqlSyncState newState) {
    std::lock_guard lock(m_callbackMutex);
    for (const auto& [id, callback] : m_syncCallbacks) {
        try {
            callback(*this, oldState, newState);
        } catch (...) {
            // Ignore callback exceptions to prevent cascading failures
        }
    }
}

} // namespace database
