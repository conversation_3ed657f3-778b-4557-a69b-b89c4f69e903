#ifndef DATABASE_SQL_OBJECT_H
#define DATABASE_SQL_OBJECT_H

#include <string>
#include <string_view>
#include <memory>
#include <atomic>
#include <shared_mutex>
#include <functional>
#include <chrono>
#include <vector>
#include <unordered_map>

#include "../sql_enums.h"
#include "../types/variant.h"
#include "../exception/sql_error.h"

namespace database {

// Forward declarations
class SqlDriver;
class SqlDatabase;
class SqlStatement;

/**
 * @brief Base class for all SQL database objects
 *
 * This class implements the dual-purpose design pattern, supporting both:
 * - Definition Mode: Creating new database objects (builder/creator pattern)
 * - Access Mode: Accessing existing database objects (database reflection)
 *
 * The class provides synchronization mechanisms to transition between modes
 * and maintain consistency with the actual database state.
 */
class SqlObject {
public:
    /**
     * @brief Synchronization callback function type
     * @param object The object that changed
     * @param oldState The previous synchronization state
     * @param newState The new synchronization state
     */
    using SyncCallback = std::function<void(const SqlObject&, SqlSyncState, SqlSyncState)>;

    /**
     * @brief Default constructor (Definition Mode)
     * Creates an object in LocalOnly state for building new database objects
     */
    SqlObject();

    /**
     * @brief Constructor with name (Definition Mode)
     * @param name The name of the database object
     */
    explicit SqlObject(std::string_view name);

    /**
     * @brief Constructor for Access Mode
     * @param name The name of the database object
     * @param driver The database driver for accessing existing objects
     */
    SqlObject(std::string_view name, SqlDriver* driver);

    /**
     * @brief Virtual destructor
     */
    virtual ~SqlObject() = default;

    // Copy and move semantics
    SqlObject(const SqlObject& other);
    SqlObject(SqlObject&& other) noexcept;
    SqlObject& operator=(const SqlObject& other);
    SqlObject& operator=(SqlObject&& other) noexcept;

    //----------------------------------------------------------------------
    // Basic Properties
    //----------------------------------------------------------------------

    /**
     * @brief Get the object name
     * @return The object name
     */
    [[nodiscard]] std::string_view name() const noexcept;

    /**
     * @brief Set the object name
     * @param name The new name
     */
    void setName(std::string_view name);

    /**
     * @brief Get the schema name
     * @return The schema name
     */
    [[nodiscard]] std::string_view schema() const noexcept;

    /**
     * @brief Set the schema name
     * @param schema The schema name
     */
    void setSchema(std::string_view schema);

    /**
     * @brief Get the fully qualified name (schema.name)
     * @return The qualified name
     */
    [[nodiscard]] std::string qualifiedName() const;

    /**
     * @brief Get the object type
     * @return The SQL object type
     */
    [[nodiscard]] virtual SqlObjectType objectType() const = 0;

    //----------------------------------------------------------------------
    // Synchronization State Management
    //----------------------------------------------------------------------

    /**
     * @brief Get the current synchronization state
     * @return The synchronization state
     */
    [[nodiscard]] SqlSyncState syncState() const noexcept;

    /**
     * @brief Check if the object is in definition mode
     * @return True if in definition mode (LocalOnly or Unsynced)
     */
    [[nodiscard]] bool isDefinitionMode() const noexcept;

    /**
     * @brief Check if the object is in access mode
     * @return True if in access mode (DatabaseOnly or Synced)
     */
    [[nodiscard]] bool isAccessMode() const noexcept;

    /**
     * @brief Check if the object exists in the database
     * @return True if the object exists in the database
     */
    [[nodiscard]] virtual bool existsInDatabase() const;

    /**
     * @brief Check if the object has local changes
     * @return True if there are unsynchronized local changes
     */
    [[nodiscard]] bool hasLocalChanges() const noexcept;

    //----------------------------------------------------------------------
    // Database Operations
    //----------------------------------------------------------------------

    /**
     * @brief Get the associated database driver
     * @return Pointer to the database driver, or nullptr if not set
     */
    [[nodiscard]] SqlDriver* driver() const noexcept;

    /**
     * @brief Set the database driver (transitions to Access Mode)
     * @param driver The database driver
     */
    void setDriver(SqlDriver* driver);

    /**
     * @brief Create the object in the database (Definition Mode)
     * @param conflictResolution How to handle conflicts if object exists
     * @return True if successful, false otherwise
     */
    virtual bool createInDatabase(SqlConflictResolution conflictResolution = SqlConflictResolution::PreferLocal);

    /**
     * @brief Load object definition from database (Access Mode)
     * @return True if successful, false otherwise
     */
    virtual bool loadFromDatabase();

    /**
     * @brief Synchronize local changes with database
     * @param conflictResolution How to handle conflicts
     * @return True if successful, false otherwise
     */
    virtual bool synchronize(SqlConflictResolution conflictResolution = SqlConflictResolution::Manual);

    /**
     * @brief Drop the object from the database
     * @return True if successful, false otherwise
     */
    virtual bool dropFromDatabase();

    //----------------------------------------------------------------------
    // SQL Generation (Definition Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Generate CREATE SQL statement
     * @return The CREATE SQL statement
     */
    [[nodiscard]] virtual std::string generateCreateSql() const = 0;

    /**
     * @brief Generate DROP SQL statement
     * @return The DROP SQL statement
     */
    [[nodiscard]] virtual std::string generateDropSql() const;

    /**
     * @brief Generate ALTER SQL statement for modifications
     * @return The ALTER SQL statement
     */
    [[nodiscard]] virtual std::string generateAlterSql() const;

    //----------------------------------------------------------------------
    // Error Handling
    //----------------------------------------------------------------------

    /**
     * @brief Get the last error
     * @return The last error that occurred
     */
    [[nodiscard]] const SqlError& lastError() const noexcept;

    /**
     * @brief Clear the last error
     */
    void clearError() noexcept;

    //----------------------------------------------------------------------
    // Synchronization Callbacks
    //----------------------------------------------------------------------

    /**
     * @brief Add a synchronization callback
     * @param callback The callback function
     * @return Callback ID for removal
     */
    size_t addSyncCallback(SyncCallback callback);

    /**
     * @brief Remove a synchronization callback
     * @param callbackId The callback ID returned by addSyncCallback
     */
    void removeSyncCallback(size_t callbackId);

protected:
    /**
     * @brief Set the synchronization state
     * @param newState The new synchronization state
     */
    void setSyncState(SqlSyncState newState);

    /**
     * @brief Set an error
     * @param error The error to set
     */
    void setError(const SqlError& error) noexcept;

    /**
     * @brief Set an error with message and code
     * @param message The error message
     * @param code The error code
     * @param sqlState The SQL state (optional)
     */
    void setError(std::string_view message, ErrorCode code, std::string_view sqlState = "");

    /**
     * @brief Execute a SQL statement
     * @param sql The SQL statement to execute
     * @return True if successful, false otherwise
     */
    bool executeSql(std::string_view sql) const;

    /**
     * @brief Create a prepared statement
     * @param sql The SQL statement
     * @return Shared pointer to the statement, or nullptr on failure
     */
    [[nodiscard]] std::shared_ptr<SqlStatement> prepareStatement(std::string_view sql) const;

    /**
     * @brief Mark the object as having local changes
     */
    void markAsModified();

    /**
     * @brief Clear the local changes flag
     */
    void clearModified();

private:
    // Object identification
    std::string m_name;
    std::string m_schema;

    // Synchronization state
    mutable std::shared_mutex m_stateMutex;
    std::atomic<SqlSyncState> m_syncState{SqlSyncState::LocalOnly};
    std::atomic<bool> m_hasLocalChanges{false};

    // Database connection
    SqlDriver* m_driver{nullptr};

    // Error handling
    mutable std::mutex m_errorMutex;
    SqlError m_lastError;

    // Synchronization callbacks
    mutable std::mutex m_callbackMutex;
    std::unordered_map<size_t, SyncCallback> m_syncCallbacks;
    std::atomic<size_t> m_nextCallbackId{1};

    // Helper methods
    void notifySyncStateChange(SqlSyncState oldState, SqlSyncState newState);
};

} // namespace database

#endif // DATABASE_SQL_OBJECT_H
