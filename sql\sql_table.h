#ifndef DATABASE_SQL_TABLE_H
#define DATABASE_SQL_TABLE_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <unordered_map>
#include <optional>

#include "sql_object.h"
#include "sql_column.h"
#include "../sql_enums.h"

namespace database {

// Forward declarations
class SqlRow;
class SqlIndex;

/**
 * @brief SQL Table abstraction with dual-purpose design
 *
 * This class supports two modes of operation:
 * 1. Definition Mode: Building table definitions for new tables
 * 2. Access Mode: Accessing existing table metadata and data from database
 *
 * The class provides a fluent interface for table definition and
 * automatic synchronization with database metadata.
 */
class SqlTable : public SqlObject {
public:
    /**
     * @brief Table metadata structure
     */
    struct Metadata {
        std::string engine;
        std::string charset;
        std::string collation;
        std::string comment;
        int64_t autoIncrementValue{1};
        bool temporary{false};

        Metadata() = default;
    };

    //----------------------------------------------------------------------
    // Constructors (Definition Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Default constructor (Definition Mode)
     */
    SqlTable();

    /**
     * @brief Constructor with name (Definition Mode)
     * @param name The table name
     */
    explicit SqlTable(std::string_view name);

    /**
     * @brief Constructor for Access Mode
     * @param name The table name
     * @param driver The database driver
     */
    SqlTable(std::string_view name, SqlDriver* driver);

    //----------------------------------------------------------------------
    // Static Factory Methods (Access Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Create table from database metadata
     * @param name The table name
     * @param driver The database driver
     * @param schema The schema name (optional)
     * @return SqlTable instance loaded from database
     */
    [[nodiscard]] static SqlTable fromDatabase(std::string_view name, SqlDriver* driver, std::string_view schema = "");

    /**
     * @brief Get all tables from database
     * @param driver The database driver
     * @param schema The schema name (optional)
     * @param type The object type filter (optional)
     * @return Vector of SqlTable instances
     */
    [[nodiscard]] static std::vector<SqlTable> getAllTables(SqlDriver* driver,
        std::string_view schema = "", SqlObjectType type = SqlObjectType::Table);

    //----------------------------------------------------------------------
    // Column Management
    //----------------------------------------------------------------------

    /**
     * @brief Add a column to the table
     * @param column The column to add
     * @return Reference to this table for method chaining
     */
    SqlTable& addColumn(const SqlColumn& column);

    /**
     * @brief Add a column with name and type
     * @param name The column name
     * @param type The column data type
     * @return Reference to this table for method chaining
     */
    SqlTable& addColumn(std::string_view name, FieldType type);

    /**
     * @brief Add a column with name, type and size
     * @param name The column name
     * @param type The column data type
     * @param size The column size
     * @return Reference to this table for method chaining
     */
    SqlTable& addColumn(std::string_view name, FieldType type, int size);

    /**
     * @brief Remove a column by name
     * @param name The column name
     * @return Reference to this table for method chaining
     */
    SqlTable& removeColumn(std::string_view name);

    /**
     * @brief Get a column by name
     * @param name The column name
     * @return Pointer to the column, or nullptr if not found
     */
    [[nodiscard]] SqlColumn* getColumn(std::string_view name);

    /**
     * @brief Get a column by name (const version)
     * @param name The column name
     * @return Pointer to the column, or nullptr if not found
     */
    [[nodiscard]] const SqlColumn* getColumn(std::string_view name) const;

    /**
     * @brief Get all columns
     * @return Vector of columns
     */
    [[nodiscard]] const std::vector<SqlColumn>& columns() const noexcept;

    /**
     * @brief Get column count
     * @return Number of columns
     */
    [[nodiscard]] size_t columnCount() const noexcept;

    /**
     * @brief Check if table has a column
     * @param name The column name
     * @return True if column exists, false otherwise
     */
    [[nodiscard]] bool hasColumn(std::string_view name) const noexcept;

    //----------------------------------------------------------------------
    // Primary Key Management
    //----------------------------------------------------------------------

    /**
     * @brief Set primary key column(s)
     * @param columnNames The column names for the primary key
     * @return Reference to this table for method chaining
     */
    SqlTable& setPrimaryKey(const std::vector<std::string>& columnNames);

    /**
     * @brief Set single column primary key
     * @param columnName The column name for the primary key
     * @return Reference to this table for method chaining
     */
    SqlTable& setPrimaryKey(std::string_view columnName);

    /**
     * @brief Get primary key column names
     * @return Vector of primary key column names
     */
    [[nodiscard]] const std::vector<std::string>& primaryKeyColumns() const noexcept;

    /**
     * @brief Check if table has a primary key
     * @return True if primary key is defined, false otherwise
     */
    [[nodiscard]] bool hasPrimaryKey() const noexcept;

    //----------------------------------------------------------------------
    // Table Metadata
    //----------------------------------------------------------------------

    /**
     * @brief Get table metadata
     * @return The table metadata
     */
    [[nodiscard]] const Metadata& metadata() const noexcept;

    /**
     * @brief Set table metadata
     * @param metadata The table metadata
     * @return Reference to this table for method chaining
     */
    SqlTable& setMetadata(const Metadata& metadata);

    /**
     * @brief Set table engine
     * @param engine The storage engine (e.g., "InnoDB", "MyISAM")
     * @return Reference to this table for method chaining
     */
    SqlTable& setEngine(std::string_view engine);

    /**
     * @brief Set table character set
     * @param charset The character set (e.g., "utf8mb4")
     * @return Reference to this table for method chaining
     */
    SqlTable& setCharset(std::string_view charset);

    /**
     * @brief Set table collation
     * @param collation The collation (e.g., "utf8mb4_unicode_ci")
     * @return Reference to this table for method chaining
     */
    SqlTable& setCollation(std::string_view collation);

    /**
     * @brief Set table comment
     * @param comment The table comment
     * @return Reference to this table for method chaining
     */
    SqlTable& setComment(std::string_view comment);

    /**
     * @brief Set as temporary table
     * @param temporary Whether the table is temporary
     * @return Reference to this table for method chaining
     */
    SqlTable& setTemporary(bool temporary);

    //----------------------------------------------------------------------
    // Data Operations (Access Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Get row count
     * @return Number of rows in the table, or -1 on error
     */
    [[nodiscard]] int64_t rowCount() const;

    /**
     * @brief Check if table is empty
     * @return True if table has no rows, false otherwise
     */
    [[nodiscard]] bool isEmpty() const;

    /**
     * @brief Truncate table (remove all rows)
     * @return True if successful, false otherwise
     */
    bool truncate();

    /**
     * @brief Insert a row into the table
     * @param row The row to insert
     * @return True if successful, false otherwise
     */
    bool insertRow(const SqlRow& row);

    /**
     * @brief Insert multiple rows into the table
     * @param rows The rows to insert
     * @return True if successful, false otherwise
     */
    bool insertRows(const std::vector<SqlRow>& rows);

    //----------------------------------------------------------------------
    // Index Management
    //----------------------------------------------------------------------

    /**
     * @brief Get all indexes for this table
     * @return Vector of indexes
     */
    [[nodiscard]] std::vector<SqlIndex> getIndexes() const;

    /**
     * @brief Create an index on this table
     * @param indexName The index name
     * @param columnNames The column names for the index
     * @param unique Whether the index is unique
     * @return True if successful, false otherwise
     */
    bool createIndex(std::string_view indexName, const std::vector<std::string>& columnNames, bool unique = false);

    /**
     * @brief Drop an index from this table
     * @param indexName The index name
     * @return True if successful, false otherwise
     */
    bool dropIndex(std::string_view indexName);

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------

    /**
     * @brief Get the object type
     * @return SqlObjectType::Table
     */
    [[nodiscard]] SqlObjectType objectType() const override;

    /**
     * @brief Check if table exists in database
     * @return True if table exists
     */
    [[nodiscard]] bool existsInDatabase() const override;

    /**
     * @brief Load table definition from database
     * @return True if successful, false otherwise
     */
    bool loadFromDatabase() override;

    /**
     * @brief Generate CREATE TABLE SQL
     * @return The CREATE TABLE SQL statement
     */
    [[nodiscard]] std::string generateCreateSql() const override;

    /**
     * @brief Generate ALTER TABLE SQL for modifications
     * @return The ALTER TABLE SQL statement
     */
    [[nodiscard]] std::string generateAlterSql() const override;

    /**
     * @brief Generate DROP TABLE SQL
     * @return The DROP TABLE SQL statement
     */
    [[nodiscard]] std::string generateDropSql() const override;

private:
    // Table structure
    std::vector<SqlColumn> m_columns;
    std::unordered_map<std::string, size_t> m_columnMap; // Maps column names to indices
    std::vector<std::string> m_primaryKeyColumns;
    Metadata m_metadata;

    // Helper methods
    void rebuildColumnMap();
    void loadColumnsFromDatabase();
    void loadMetadataFromDatabase();
    std::string generatePrimaryKeyConstraint() const;
    std::string generateTableOptions() const;
};

// Type aliases for compatibility with existing code
using Table = SqlTable;

} // namespace database

#endif // DATABASE_SQL_TABLE_H
