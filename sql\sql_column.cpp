#include "sql_column.h"

#include <format>
#include <algorithm>

#include "sql_table.h"
#include "../driver/sql_driver.h"
#include "../driver/sql_field.h"
#include "../driver/sql_statement.h"

namespace database {

SqlColumn::SqlColumn() : SqlObject() {
}

SqlColumn::SqlColumn(std::string_view name) : SqlObject(name) {
}

SqlColumn::SqlColumn(std::string_view name, FieldType type)
    : SqlObject(name), m_dataType(type) {
}

SqlColumn::SqlColumn(std::string_view name, FieldType type, int size)
    : SqlObject(name), m_dataType(type), m_size(size) {
}

SqlColumn::SqlColumn(std::string_view name, SqlTable* table, SqlDriver* driver)
    : SqlObject(name, driver), m_table(table) {
    if (table) {
        m_tableName = table->name();
    }
}

SqlColumn SqlColumn::fromDatabase(std::string_view name, SqlTable* table, SqlDriver* driver) {
    SqlColumn column(name, table, driver);
    column.loadFromDatabase();
    return column;
}

SqlColumn SqlColumn::fromField(const SqlField& field, SqlTable* table, SqlDriver* driver) {
    SqlColumn column(field.name(), table, driver);

    // Copy field properties
    column.m_dataType = field.type();
    column.m_nullable = !field.isRequired();
    column.m_autoIncrement = field.isAutoIncrement();
    column.m_defaultValue = field.defaultValue();

    if (field.isPrimaryKey()) {
        column.addConstraint(Constraint(SqlConstraintType::PrimaryKey));
    }

    column.setSyncState(SqlSyncState::Synced);
    return column;
}

FieldType SqlColumn::dataType() const noexcept {
    return m_dataType;
}

SqlColumn& SqlColumn::setDataType(FieldType type) {
    if (m_dataType != type) {
        m_dataType = type;
        markAsModified();
    }
    return *this;
}

int SqlColumn::size() const noexcept {
    return m_size;
}

SqlColumn& SqlColumn::setSize(int size) {
    if (m_size != size) {
        m_size = size;
        markAsModified();
    }
    return *this;
}

int SqlColumn::precision() const noexcept {
    return m_precision;
}

SqlColumn& SqlColumn::setPrecision(int precision) {
    if (m_precision != precision) {
        m_precision = precision;
        markAsModified();
    }
    return *this;
}

int SqlColumn::scale() const noexcept {
    return m_scale;
}

SqlColumn& SqlColumn::setScale(int scale) {
    if (m_scale != scale) {
        m_scale = scale;
        markAsModified();
    }
    return *this;
}

bool SqlColumn::isNullable() const noexcept {
    return m_nullable;
}

SqlColumn& SqlColumn::setNullable(bool nullable) {
    if (m_nullable != nullable) {
        m_nullable = nullable;
        markAsModified();
    }
    return *this;
}

const Variant& SqlColumn::defaultValue() const noexcept {
    return m_defaultValue;
}

SqlColumn& SqlColumn::setDefaultValue(const Variant& value) {
    if (!(m_defaultValue == value)) {
        m_defaultValue = value;
        markAsModified();
    }
    return *this;
}

bool SqlColumn::isAutoIncrement() const noexcept {
    return m_autoIncrement;
}

SqlColumn& SqlColumn::setAutoIncrement(bool autoIncrement) {
    if (m_autoIncrement != autoIncrement) {
        m_autoIncrement = autoIncrement;
        markAsModified();
    }
    return *this;
}

SqlTable* SqlColumn::table() const noexcept {
    return m_table;
}

void SqlColumn::setTable(SqlTable* table) {
    m_table = table;
    if (table) {
        m_tableName = table->name();
    }
}

std::string SqlColumn::tableName() const {
    if (m_table) {
        return std::string(m_table->name());
    }
    return m_tableName;
}

void SqlColumn::setTableName(std::string_view tableName) {
    m_tableName = tableName;
}

SqlColumn& SqlColumn::addConstraint(const Constraint& constraint) {
    // Remove existing constraint of the same type
    removeConstraint(constraint.type);
    m_constraints.push_back(constraint);
    markAsModified();
    return *this;
}

SqlColumn& SqlColumn::removeConstraint(SqlConstraintType type) {
    auto it = std::remove_if(m_constraints.begin(), m_constraints.end(),
        [type](const Constraint& c) { return c.type == type; });

    if (it != m_constraints.end()) {
        m_constraints.erase(it, m_constraints.end());
        markAsModified();
    }
    return *this;
}

const std::vector<SqlColumn::Constraint>& SqlColumn::constraints() const noexcept {
    return m_constraints;
}

bool SqlColumn::hasConstraint(SqlConstraintType type) const noexcept {
    return std::any_of(m_constraints.begin(), m_constraints.end(),
        [type](const Constraint& c) { return c.type == type; });
}

std::optional<SqlColumn::Constraint> SqlColumn::getConstraint(SqlConstraintType type) const {
    auto it = std::find_if(m_constraints.begin(), m_constraints.end(),
        [type](const Constraint& c) { return c.type == type; });

    if (it != m_constraints.end()) {
        return *it;
    }
    return std::nullopt;
}

SqlColumn& SqlColumn::primaryKey() {
    addConstraint(Constraint(SqlConstraintType::PrimaryKey));
    setNullable(false);
    return *this;
}

SqlColumn& SqlColumn::unique() {
    addConstraint(Constraint(SqlConstraintType::Unique));
    return *this;
}

SqlColumn& SqlColumn::notNull() {
    setNullable(false);
    addConstraint(Constraint(SqlConstraintType::NotNull));
    return *this;
}

SqlColumn& SqlColumn::autoIncrement() {
    setAutoIncrement(true);
    addConstraint(Constraint(SqlConstraintType::AutoIncrement));
    return *this;
}

SqlColumn& SqlColumn::defaultValue(const Variant& value) {
    setDefaultValue(value);
    addConstraint(Constraint(SqlConstraintType::Default, "", value));
    return *this;
}

SqlColumn& SqlColumn::foreignKey(std::string_view referencedTable, std::string_view referencedColumn) {
    Constraint fk(SqlConstraintType::ForeignKey);
    fk.referencedTable = referencedTable;
    fk.referencedColumn = referencedColumn;
    addConstraint(fk);
    return *this;
}

SqlColumn& SqlColumn::check(std::string_view condition) {
    Constraint check(SqlConstraintType::Check);
    check.value = Variant(std::string(condition));
    addConstraint(check);
    return *this;
}

SqlObjectType SqlColumn::objectType() const {
    return SqlObjectType::Table; // Columns are part of tables
}

bool SqlColumn::existsInDatabase() const {
    if (!driver() || !m_table) {
        return false;
    }

    // Check if the parent table exists and contains this column
    if (!m_table->existsInDatabase()) {
        return false;
    }

    // Load table columns and check if this column exists
    // This is a simplified check - in practice, we'd query the database schema
    return true;
}

bool SqlColumn::loadFromDatabase() {
    if (!driver() || tableName().empty() || name().empty()) {
        setError("Insufficient information to load column from database", ErrorCode::InvalidArgument);
        return false;
    }

    loadMetadataFromDatabase();
    setSyncState(SqlSyncState::Synced);
    clearModified();
    clearError();

    return true;
}

std::string SqlColumn::generateCreateSql() const {
    std::string sql = std::format("{} {}", name(), sqlTypeString());

    // Add constraints
    if (!m_nullable) {
        sql += " NOT NULL";
    }

    if (m_autoIncrement) {
        sql += " AUTOINCREMENT";
    }

    if (!m_defaultValue.isNull()) {
        sql += std::format(" DEFAULT {}", m_defaultValue.toString());
    }

    // Add other constraints
    std::string constraintSql = generateConstraintSql();
    if (!constraintSql.empty()) {
        sql += " " + constraintSql;
    }

    return sql;
}

std::string SqlColumn::generateAlterSql() const {
    if (tableName().empty()) {
        return "";
    }

    return std::format("ALTER TABLE {} MODIFY COLUMN {}",
        tableName(), generateCreateSql());
}

std::string SqlColumn::sqlTypeString() const {
    std::string typeStr = fieldTypeToSqlString(m_dataType);

    if (m_size > 0) {
        if (m_precision > 0 && m_scale >= 0) {
            typeStr += std::format("({},{})", m_precision, m_scale);
        } else {
            typeStr += std::format("({})", m_size);
        }
    }

    return typeStr;
}

std::string SqlColumn::qualifiedColumnName() const {
    std::string table = tableName();
    if (table.empty()) {
        return std::string(name());
    }
    return std::format("{}.{}", table, name());
}

void SqlColumn::loadMetadataFromDatabase() {
    // This would typically query the database schema tables
    // For now, this is a placeholder implementation
    // Derived classes or database-specific implementations would override this
}

std::string SqlColumn::generateConstraintSql() const {
    std::string sql;

    for (const auto& constraint : m_constraints) {
        switch (constraint.type) {
            case SqlConstraintType::PrimaryKey:
                sql += " PRIMARY KEY";
                break;
            case SqlConstraintType::Unique:
                sql += " UNIQUE";
                break;
            case SqlConstraintType::ForeignKey:
                if (!constraint.referencedTable.empty() && !constraint.referencedColumn.empty()) {
                    sql += std::format(" REFERENCES {}({})",
                        constraint.referencedTable, constraint.referencedColumn);
                }
                break;
            case SqlConstraintType::Check:
                if (!constraint.value.isNull()) {
                    sql += std::format(" CHECK ({})", constraint.value.toString());
                }
                break;
            default:
                break;
        }
    }

    return sql;
}

std::string SqlColumn::fieldTypeToSqlString(FieldType type) const {
    switch (type) {
        case FieldType::Integer:    return "INTEGER";
        case FieldType::BigInt:     return "BIGINT";
        case FieldType::Float:      return "FLOAT";
        case FieldType::Double:     return "DOUBLE";
        case FieldType::Decimal:    return "DECIMAL";
        case FieldType::Char:       return "CHAR";
        case FieldType::VarChar:    return "VARCHAR";
        case FieldType::Text:       return "TEXT";
        case FieldType::Date:       return "DATE";
        case FieldType::Time:       return "TIME";
        case FieldType::DateTime:   return "DATETIME";
        case FieldType::Timestamp:  return "TIMESTAMP";
        case FieldType::Boolean:    return "BOOLEAN";
        case FieldType::Blob:       return "BLOB";
        case FieldType::Binary:     return "BINARY";
        default:                    return "TEXT";
    }
}

} // namespace database
