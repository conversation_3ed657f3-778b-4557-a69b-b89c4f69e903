#include "sql_row.h"

#include <format>
#include <algorithm>
#include <sstream>

#include "sql_table.h"
#include "sql_column.h"
#include "../driver/sql_driver.h"
#include "../driver/sql_statement.h"
#include "../driver/sql_record.h"

namespace database {

SqlRow::SqlRow() : SqlObject() {
}

SqlRow::SqlRow(SqlTable* table) : SqlObject(), m_table(table) {
    if (table && table->driver()) {
        setDriver(table->driver());
    }
}

SqlRow::SqlRow(const std::unordered_map<std::string, Variant>& values)
    : SqlObject(), m_values(values) {
}

SqlRow::SqlRow(SqlTable* table, const std::unordered_map<std::string, Variant>& values)
    : SqlObject(), m_table(table), m_values(values) {
    if (table && table->driver()) {
        setDriver(table->driver());
    }
}

SqlRow::SqlRow(SqlTable* table, SqlDriver* driver, const std::unordered_map<std::string, Variant>& primaryKeyValues)
    : SqlObject("", driver), m_table(table), m_primaryKeyValues(primaryKeyValues), m_rowState(RowState::Clean) {
}

SqlRow SqlRow::fromDatabase(SqlTable* table, SqlDriver* driver,
    const std::unordered_map<std::string, Variant>& primaryKeyValues) {
    SqlRow row(table, driver, primaryKeyValues);
    row.loadFromDatabase();
    return row;
}

SqlRow SqlRow::fromRecord(const SqlRecord& record, SqlTable* table, SqlDriver* driver) {
    SqlRow row(table, driver, {});

    // Copy values from record
    for (size_t i = 0; i < record.count(); ++i) {
        const auto& field = record.field(i);
        row.setValue(field.name(), field.value());
    }

    // Extract primary key values if table is available
    if (table && table->hasPrimaryKey()) {
        std::unordered_map<std::string, Variant> pkValues;
        for (const auto& pkCol : table->primaryKeyColumns()) {
            if (row.hasValue(pkCol)) {
                pkValues[pkCol] = row.getValue(pkCol);
            }
        }
        row.setPrimaryKeyValues(pkValues);
    }

    row.setRowState(RowState::Clean);
    row.updateOriginalValues();
    row.setSyncState(SqlSyncState::Synced);

    return row;
}

SqlRow& SqlRow::setValue(std::string_view columnName, const Variant& value) {
    std::string colName(columnName);

    // Check if value actually changed
    auto it = m_values.find(colName);
    if (it != m_values.end() && it->second == value) {
        return *this; // No change
    }

    m_values[colName] = value;

    // Update row state if not new
    if (m_rowState == RowState::Clean) {
        setRowState(RowState::Modified);
    }

    markAsModified();
    return *this;
}

Variant SqlRow::getValue(std::string_view columnName) const {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        return it->second;
    }
    return Variant();
}

bool SqlRow::hasValue(std::string_view columnName) const noexcept {
    return m_values.find(std::string(columnName)) != m_values.end();
}

SqlRow& SqlRow::removeValue(std::string_view columnName) {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        m_values.erase(it);

        if (m_rowState == RowState::Clean) {
            setRowState(RowState::Modified);
        }

        markAsModified();
    }
    return *this;
}

const std::unordered_map<std::string, Variant>& SqlRow::values() const noexcept {
    return m_values;
}

SqlRow& SqlRow::setValues(const std::unordered_map<std::string, Variant>& values) {
    m_values = values;

    if (m_rowState == RowState::Clean) {
        setRowState(RowState::Modified);
    }

    markAsModified();
    return *this;
}

SqlRow& SqlRow::clearValues() {
    if (!m_values.empty()) {
        m_values.clear();

        if (m_rowState == RowState::Clean) {
            setRowState(RowState::Modified);
        }

        markAsModified();
    }
    return *this;
}

SqlTable* SqlRow::table() const noexcept {
    return m_table;
}

void SqlRow::setTable(SqlTable* table) {
    m_table = table;
    if (table && table->driver()) {
        setDriver(table->driver());
    }
}

std::string SqlRow::tableName() const {
    if (m_table) {
        return std::string(m_table->name());
    }
    return "";
}

SqlRow::RowState SqlRow::rowState() const noexcept {
    return m_rowState;
}

bool SqlRow::isNew() const noexcept {
    return m_rowState == RowState::New;
}

bool SqlRow::isClean() const noexcept {
    return m_rowState == RowState::Clean;
}

bool SqlRow::isModified() const noexcept {
    return m_rowState == RowState::Modified;
}

bool SqlRow::isDeleted() const noexcept {
    return m_rowState == RowState::Deleted;
}

SqlRow& SqlRow::markDeleted() {
    setRowState(RowState::Deleted);
    markAsModified();
    return *this;
}

SqlRow& SqlRow::unmarkDeleted() {
    if (m_rowState == RowState::Deleted) {
        // Restore to previous state based on whether we have original values
        if (m_originalValues.empty()) {
            setRowState(RowState::New);
        } else {
            setRowState(RowState::Modified);
        }
    }
    return *this;
}

std::unordered_map<std::string, Variant> SqlRow::primaryKeyValues() const {
    if (!m_primaryKeyValues.empty()) {
        return m_primaryKeyValues;
    }

    // Extract from current values if table has primary key
    if (m_table && m_table->hasPrimaryKey()) {
        std::unordered_map<std::string, Variant> pkValues;
        for (const auto& pkCol : m_table->primaryKeyColumns()) {
            if (hasValue(pkCol)) {
                pkValues[pkCol] = getValue(pkCol);
            }
        }
        return pkValues;
    }

    return {};
}

SqlRow& SqlRow::setPrimaryKeyValues(const std::unordered_map<std::string, Variant>& pkValues) {
    m_primaryKeyValues = pkValues;

    // Also set these values in the main values map
    for (const auto& [key, value] : pkValues) {
        m_values[key] = value;
    }

    return *this;
}

bool SqlRow::insert() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!m_table) {
        setError("No table associated with row", ErrorCode::InvalidArgument);
        return false;
    }

    if (!isNew()) {
        setError("Row is not new, use update() instead", ErrorCode::InvalidArgument);
        return false;
    }

    std::string sql = generateInsertSql();
    if (sql.empty()) {
        setError("Failed to generate INSERT SQL", ErrorCode::InternalError);
        return false;
    }

    auto stmt = prepareStatement(sql);
    if (!stmt) {
        return false;
    }

    // Bind parameters
    int paramIndex = 1;
    for (const auto& column : m_table->columns()) {
        if (hasValue(column.name())) {
            stmt->bindValue(paramIndex++, getValue(column.name()));
        } else if (!column.defaultValue().isNull()) {
            stmt->bindValue(paramIndex++, column.defaultValue());
        } else {
            stmt->bindValue(paramIndex++, Variant());
        }
    }

    if (!stmt->execute()) {
        setError("Failed to execute INSERT statement", ErrorCode::ExecutionFailed);
        return false;
    }

    // Update state
    setRowState(RowState::Clean);
    updateOriginalValues();
    setSyncState(SqlSyncState::Synced);
    clearError();

    return true;
}

bool SqlRow::update() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!m_table) {
        setError("No table associated with row", ErrorCode::InvalidArgument);
        return false;
    }

    if (isNew()) {
        setError("Row is new, use insert() instead", ErrorCode::InvalidArgument);
        return false;
    }

    if (!isModified()) {
        return true; // Nothing to update
    }

    std::string sql = generateUpdateSql();
    if (sql.empty()) {
        setError("Failed to generate UPDATE SQL", ErrorCode::InternalError);
        return false;
    }

    auto stmt = prepareStatement(sql);
    if (!stmt) {
        return false;
    }

    // Bind parameters (changed values + primary key values)
    int paramIndex = 1;

    // Bind changed values
    for (const auto& [colName, value] : m_values) {
        auto origIt = m_originalValues.find(colName);
        if (origIt == m_originalValues.end() || !(origIt->second == value)) {
            stmt->bindValue(paramIndex++, value);
        }
    }

    // Bind primary key values for WHERE clause
    auto pkValues = primaryKeyValues();
    for (const auto& [pkCol, pkValue] : pkValues) {
        stmt->bindValue(paramIndex++, pkValue);
    }

    if (!stmt->execute()) {
        setError("Failed to execute UPDATE statement", ErrorCode::ExecutionFailed);
        return false;
    }

    // Update state
    setRowState(RowState::Clean);
    updateOriginalValues();
    clearError();

    return true;
}

bool SqlRow::deleteFromDatabase() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!m_table) {
        setError("No table associated with row", ErrorCode::InvalidArgument);
        return false;
    }

    if (isNew()) {
        setError("Row is new and not in database", ErrorCode::InvalidArgument);
        return false;
    }

    std::string sql = generateDeleteSql();
    if (sql.empty()) {
        setError("Failed to generate DELETE SQL", ErrorCode::InternalError);
        return false;
    }

    auto stmt = prepareStatement(sql);
    if (!stmt) {
        return false;
    }

    // Bind primary key values for WHERE clause
    int paramIndex = 1;
    auto pkValues = primaryKeyValues();
    for (const auto& [pkCol, pkValue] : pkValues) {
        stmt->bindValue(paramIndex++, pkValue);
    }

    if (!stmt->execute()) {
        setError("Failed to execute DELETE statement", ErrorCode::ExecutionFailed);
        return false;
    }

    // Update state
    setRowState(RowState::Deleted);
    clearError();

    return true;
}

bool SqlRow::save() {
    if (isNew()) {
        return insert();
    } else if (isModified()) {
        return update();
    }
    return true; // Nothing to save
}

bool SqlRow::reload() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!m_table) {
        setError("No table associated with row", ErrorCode::InvalidArgument);
        return false;
    }

    if (isNew()) {
        setError("Cannot reload new row", ErrorCode::InvalidArgument);
        return false;
    }

    return loadFromDatabase();
}

SqlObjectType SqlRow::objectType() const {
    return SqlObjectType::Table; // Rows are part of tables
}

bool SqlRow::existsInDatabase() const {
    if (!driver() || !m_table || isNew()) {
        return false;
    }

    auto pkValues = primaryKeyValues();
    if (pkValues.empty()) {
        return false;
    }

    // Generate SELECT query to check existence
    std::ostringstream sql;
    sql << "SELECT 1 FROM " << m_table->qualifiedName() << " WHERE ";

    bool first = true;
    for (const auto& [pkCol, pkValue] : pkValues) {
        if (!first) sql << " AND ";
        sql << pkCol << " = ?";
        first = false;
    }

    auto stmt = prepareStatement(sql.str());
    if (!stmt) {
        return false;
    }

    // Bind primary key values
    int paramIndex = 1;
    for (const auto& [pkCol, pkValue] : pkValues) {
        stmt->bindValue(paramIndex++, pkValue);
    }

    if (!stmt->execute()) {
        return false;
    }

    return stmt->step(); // Returns true if row exists
}

bool SqlRow::loadFromDatabase() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!m_table) {
        setError("No table associated with row", ErrorCode::InvalidArgument);
        return false;
    }

    auto pkValues = primaryKeyValues();
    if (pkValues.empty()) {
        setError("No primary key values specified", ErrorCode::InvalidArgument);
        return false;
    }

    // Generate SELECT query
    std::ostringstream sql;
    sql << "SELECT ";

    // Add column names
    bool first = true;
    for (const auto& column : m_table->columns()) {
        if (!first) sql << ", ";
        sql << column.name();
        first = false;
    }

    sql << " FROM " << m_table->qualifiedName() << " WHERE ";

    // Add WHERE clause for primary key
    first = true;
    for (const auto& [pkCol, pkValue] : pkValues) {
        if (!first) sql << " AND ";
        sql << pkCol << " = ?";
        first = false;
    }

    auto stmt = prepareStatement(sql.str());
    if (!stmt) {
        return false;
    }

    // Bind primary key values
    int paramIndex = 1;
    for (const auto& [pkCol, pkValue] : pkValues) {
        stmt->bindValue(paramIndex++, pkValue);
    }

    if (!stmt->execute()) {
        setError("Failed to execute SELECT statement", ErrorCode::ExecutionFailed);
        return false;
    }

    if (!stmt->step()) {
        setError("Row not found in database", ErrorCode::ExecutionFailed);
        return false;
    }

    // Load values from result
    m_values.clear();
    for (size_t i = 0; i < m_table->columns().size(); ++i) {
        const auto& column = m_table->columns()[i];
        m_values[std::string(column.name())] = stmt->value(static_cast<int>(i));
    }

    // Update state
    setRowState(RowState::Clean);
    updateOriginalValues();
    setSyncState(SqlSyncState::Synced);
    clearError();

    return true;
}

std::string SqlRow::generateCreateSql() const {
    return generateInsertSql();
}

std::string SqlRow::generateAlterSql() const {
    return generateUpdateSql();
}

size_t SqlRow::columnCount() const noexcept {
    return m_values.size();
}

bool SqlRow::isEmpty() const noexcept {
    return m_values.empty();
}

SqlRecord SqlRow::toRecord() const {
    std::unordered_map<std::string, Variant> recordData;
    for (const auto& [colName, value] : m_values) {
        recordData[colName] = value;
    }
    return SqlRecord(recordData);
}

void SqlRow::setRowState(RowState state) {
    m_rowState = state;
}

std::string SqlRow::generateInsertSql() const {
    if (!m_table || m_values.empty()) {
        return "";
    }

    std::ostringstream sql;
    sql << "INSERT INTO " << m_table->qualifiedName() << " (";

    // Add column names
    bool first = true;
    for (const auto& column : m_table->columns()) {
        if (!first) sql << ", ";
        sql << column.name();
        first = false;
    }

    sql << ") VALUES (";

    // Add parameter placeholders
    first = true;
    for (const auto& column : m_table->columns()) {
        if (!first) sql << ", ";
        sql << "?";
        first = false;
    }

    sql << ")";
    return sql.str();
}

std::string SqlRow::generateUpdateSql() const {
    if (!m_table || m_values.empty()) {
        return "";
    }

    std::ostringstream sql;
    sql << "UPDATE " << m_table->qualifiedName() << " SET ";

    // Add changed columns
    bool first = true;
    for (const auto& [colName, value] : m_values) {
        auto origIt = m_originalValues.find(colName);
        if (origIt == m_originalValues.end() || !(origIt->second == value)) {
            if (!first) sql << ", ";
            sql << colName << " = ?";
            first = false;
        }
    }

    if (first) {
        return ""; // No changes to update
    }

    // Add WHERE clause for primary key
    sql << " WHERE " << generateWherePrimaryKey();

    return sql.str();
}

std::string SqlRow::generateDeleteSql() const {
    if (!m_table) {
        return "";
    }

    std::ostringstream sql;
    sql << "DELETE FROM " << m_table->qualifiedName();
    sql << " WHERE " << generateWherePrimaryKey();

    return sql.str();
}

std::string SqlRow::generateWherePrimaryKey() const {
    auto pkValues = primaryKeyValues();
    if (pkValues.empty()) {
        return "1=0"; // Invalid WHERE clause
    }

    std::ostringstream sql;
    bool first = true;
    for (const auto& [pkCol, pkValue] : pkValues) {
        if (!first) sql << " AND ";
        sql << pkCol << " = ?";
        first = false;
    }

    return sql.str();
}

void SqlRow::updateOriginalValues() {
    m_originalValues = m_values;
}

} // namespace database