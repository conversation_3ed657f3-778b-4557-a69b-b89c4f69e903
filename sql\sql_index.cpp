#include "sql_index.h"

#include <format>
#include <algorithm>
#include <sstream>

#include "sql_table.h"
#include "sql_column.h"
#include "../driver/sql_driver.h"
#include "../driver/sql_statement.h"

namespace database {

SqlIndex::SqlIndex() : SqlObject() {
}

SqlIndex::SqlIndex(std::string_view name) : SqlObject(name) {
    m_metadata.name = name;
}

SqlIndex::SqlIndex(std::string_view name, SqlTable* table)
    : SqlObject(name), m_table(table) {
    m_metadata.name = name;
    if (table) {
        m_tableName = table->name();
        m_metadata.tableName = m_tableName;
        if (table->driver()) {
            setDriver(table->driver());
        }
    }
}

SqlIndex::SqlIndex(std::string_view name, SqlTable* table, SqlDriver* driver)
    : SqlObject(name, driver), m_table(table) {
    m_metadata.name = name;
    if (table) {
        m_tableName = table->name();
        m_metadata.tableName = m_tableName;
    }
}

SqlIndex SqlIndex::fromDatabase(std::string_view name, SqlTable* table, SqlDriver* driver) {
    SqlIndex index(name, table, driver);
    index.loadFromDatabase();
    return index;
}

SqlIndex SqlIndex::fromMetadata(const Metadata& metadata, SqlTable* table, SqlDriver* driver) {
    SqlIndex index(metadata.name, table, driver);
    index.setMetadata(metadata);

    // Parse column names from metadata
    for (const auto& colName : metadata.columnNames) {
        index.addColumn(colName);
    }

    index.setSyncState(SqlSyncState::Synced);
    return index;
}

std::vector<SqlIndex> SqlIndex::getAllIndexes(SqlTable* table, SqlDriver* driver) {
    std::vector<SqlIndex> indexes;

    if (!table || !driver) {
        return indexes;
    }

    try {
        auto indexMetadata = driver->getIndexes(*table);
        indexes.reserve(indexMetadata.size());

        for (const auto& metadata : indexMetadata) {
            indexes.push_back(fromMetadata(metadata, table, driver));
        }
    } catch (...) {
        // Handle exceptions gracefully
    }

    return indexes;
}

bool SqlIndex::isUnique() const noexcept {
    return m_metadata.unique;
}

SqlIndex& SqlIndex::setUnique(bool unique) {
    if (m_metadata.unique != unique) {
        m_metadata.unique = unique;
        markAsModified();
    }
    return *this;
}

bool SqlIndex::isPrimary() const noexcept {
    return m_metadata.primary;
}

SqlIndex& SqlIndex::setPrimary(bool primary) {
    if (m_metadata.primary != primary) {
        m_metadata.primary = primary;
        if (primary) {
            m_metadata.unique = true; // Primary keys are always unique
        }
        markAsModified();
    }
    return *this;
}

bool SqlIndex::isClustered() const noexcept {
    return m_metadata.clustered;
}

SqlIndex& SqlIndex::setClustered(bool clustered) {
    if (m_metadata.clustered != clustered) {
        m_metadata.clustered = clustered;
        markAsModified();
    }
    return *this;
}

SqlIndex& SqlIndex::addColumn(std::string_view columnName, SqlSortOrder sortOrder) {
    // Check if column already exists
    if (hasColumn(columnName)) {
        removeColumn(columnName);
    }

    m_columns.emplace_back(columnName, sortOrder);
    markAsModified();
    return *this;
}

SqlIndex& SqlIndex::addColumn(std::string_view columnName, int length, SqlSortOrder sortOrder) {
    // Check if column already exists
    if (hasColumn(columnName)) {
        removeColumn(columnName);
    }

    IndexColumn indexCol(columnName, sortOrder);
    indexCol.length = length;
    m_columns.push_back(indexCol);
    markAsModified();
    return *this;
}

SqlIndex& SqlIndex::addColumn(const IndexColumn& indexColumn) {
    // Check if column already exists
    if (hasColumn(indexColumn.columnName)) {
        removeColumn(indexColumn.columnName);
    }

    m_columns.push_back(indexColumn);
    markAsModified();
    return *this;
}

SqlIndex& SqlIndex::removeColumn(std::string_view columnName) {
    auto it = std::remove_if(m_columns.begin(), m_columns.end(),
        [columnName](const IndexColumn& col) { return col.columnName == columnName; });

    if (it != m_columns.end()) {
        m_columns.erase(it, m_columns.end());
        markAsModified();
    }

    return *this;
}

const std::vector<SqlIndex::IndexColumn>& SqlIndex::columns() const noexcept {
    return m_columns;
}

size_t SqlIndex::columnCount() const noexcept {
    return m_columns.size();
}

bool SqlIndex::hasColumn(std::string_view columnName) const noexcept {
    return std::any_of(m_columns.begin(), m_columns.end(),
        [columnName](const IndexColumn& col) { return col.columnName == columnName; });
}

std::vector<std::string> SqlIndex::columnNames() const {
    std::vector<std::string> names;
    names.reserve(m_columns.size());

    for (const auto& col : m_columns) {
        names.push_back(col.columnName);
    }

    return names;
}

SqlTable* SqlIndex::table() const noexcept {
    return m_table;
}

void SqlIndex::setTable(SqlTable* table) {
    m_table = table;
    if (table) {
        m_tableName = table->name();
        m_metadata.tableName = m_tableName;
        if (table->driver()) {
            setDriver(table->driver());
        }
    }
}

std::string SqlIndex::tableName() const {
    if (m_table) {
        return std::string(m_table->name());
    }
    return m_tableName;
}

void SqlIndex::setTableName(std::string_view tableName) {
    m_tableName = tableName;
    m_metadata.tableName = tableName;
}

const SqlIndex::Metadata& SqlIndex::metadata() const noexcept {
    return m_metadata;
}

SqlIndex& SqlIndex::setMetadata(const Metadata& metadata) {
    m_metadata = metadata;
    markAsModified();
    return *this;
}

SqlIndex& SqlIndex::unique() {
    return setUnique(true);
}

SqlIndex& SqlIndex::primary() {
    return setPrimary(true);
}

SqlIndex& SqlIndex::clustered() {
    return setClustered(true);
}

SqlIndex& SqlIndex::asc(std::string_view columnName) {
    return addColumn(columnName, SqlSortOrder::Ascending);
}

SqlIndex& SqlIndex::desc(std::string_view columnName) {
    return addColumn(columnName, SqlSortOrder::Descending);
}

SqlObjectType SqlIndex::objectType() const {
    if (m_metadata.unique) {
        return SqlObjectType::UniqueIndex;
    }
    return SqlObjectType::Index;
}

bool SqlIndex::existsInDatabase() const {
    if (!driver() || name().empty()) {
        return false;
    }

    // Use driver's method to check if index exists
    try {
        auto metadata = driver()->getIndexMetadata(*this);
        return !metadata.name.empty();
    } catch (...) {
        return false;
    }
}

bool SqlIndex::loadFromDatabase() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (name().empty()) {
        setError("Index name not specified", ErrorCode::InvalidArgument);
        return false;
    }

    if (!existsInDatabase()) {
        setError("Index does not exist in database", ErrorCode::ExecutionFailed);
        return false;
    }

    loadMetadataFromDatabase();
    setSyncState(SqlSyncState::Synced);
    clearModified();
    clearError();

    return true;
}

std::string SqlIndex::generateCreateSql() const {
    if (m_columns.empty() || tableName().empty()) {
        return "";
    }

    std::ostringstream sql;

    // CREATE INDEX clause
    sql << "CREATE ";

    if (m_metadata.unique) {
        sql << "UNIQUE ";
    }

    if (m_metadata.clustered) {
        sql << "CLUSTERED ";
    }

    sql << "INDEX " << name() << " ON " << tableName();

    // Add column list
    sql << " (" << generateColumnList() << ")";

    return sql.str();
}

std::string SqlIndex::generateDropSql() const {
    if (name().empty()) {
        return "";
    }

    return std::format("DROP INDEX {}", qualifiedIndexName());
}

std::string SqlIndex::qualifiedIndexName() const {
    std::string table = tableName();
    if (table.empty()) {
        return std::string(name());
    }
    return std::format("{}.{}", table, name());
}

bool SqlIndex::isEmpty() const noexcept {
    return m_columns.empty();
}

void SqlIndex::loadMetadataFromDatabase() {
    if (!driver()) {
        return;
    }

    try {
        m_metadata = driver()->getIndexMetadata(*this);

        // Clear existing columns and load from metadata
        m_columns.clear();
        for (const auto& colName : m_metadata.columnNames) {
            m_columns.emplace_back(colName);
        }
    } catch (...) {
        // Handle exceptions gracefully
    }
}

std::string SqlIndex::generateColumnList() const {
    std::ostringstream sql;

    bool first = true;
    for (const auto& col : m_columns) {
        if (!first) sql << ", ";

        sql << col.columnName;

        if (col.length > 0) {
            sql << "(" << col.length << ")";
        }

        if (col.sortOrder == SqlSortOrder::Descending) {
            sql << " DESC";
        }

        first = false;
    }

    return sql.str();
}

} // namespace database
