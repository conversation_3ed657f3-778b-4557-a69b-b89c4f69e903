#ifndef DATABASE_SQL_ROW_H
#define DATABASE_SQL_ROW_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <unordered_map>
#include <optional>

#include "sql_object.h"
#include "../types/variant.h"

namespace database {

// Forward declarations
class SqlTable;
class SqlColumn;

/**
 * @brief SQL Row abstraction with dual-purpose design
 *
 * This class supports two modes of operation:
 * 1. Definition Mode: Building row data for INSERT/UPDATE operations
 * 2. Access Mode: Accessing existing row data from database queries
 *
 * The class provides a fluent interface for row manipulation and
 * automatic synchronization with database state.
 */
class SqlRow : public SqlObject {
public:
    /**
     * @brief Row state enumeration
     */
    enum class RowState {
        New,        ///< New row, not yet in database
        Clean,      ///< Row loaded from database, no changes
        Modified,   ///< Row loaded from database with local changes
        Deleted     ///< Row marked for deletion
    };

    //----------------------------------------------------------------------
    // Constructors (Definition Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Default constructor (Definition Mode)
     */
    SqlRow();

    /**
     * @brief Constructor with table association (Definition Mode)
     * @param table The associated table
     */
    explicit SqlRow(SqlTable* table);

    /**
     * @brief Constructor with values (Definition Mode)
     * @param values The column name-value pairs
     */
    explicit SqlRow(const std::unordered_map<std::string, Variant>& values);

    /**
     * @brief Constructor with table and values (Definition Mode)
     * @param table The associated table
     * @param values The column name-value pairs
     */
    SqlRow(SqlTable* table, const std::unordered_map<std::string, Variant>& values);

    /**
     * @brief Constructor for Access Mode
     * @param table The associated table
     * @param driver The database driver
     * @param primaryKeyValues The primary key values for this row
     */
    SqlRow(SqlTable* table, SqlDriver* driver, const std::unordered_map<std::string, Variant>& primaryKeyValues);

    //----------------------------------------------------------------------
    // Static Factory Methods (Access Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Create row from database by primary key
     * @param table The associated table
     * @param driver The database driver
     * @param primaryKeyValues The primary key values
     * @return SqlRow instance loaded from database
     */
    [[nodiscard]] static SqlRow fromDatabase(SqlTable* table, SqlDriver* driver,
        const std::unordered_map<std::string, Variant>& primaryKeyValues);

    /**
     * @brief Create row from SQL record
     * @param record The SQL record containing row data
     * @param table The associated table
     * @param driver The database driver
     * @return SqlRow instance
     */
    [[nodiscard]] static SqlRow fromRecord(const class SqlRecord& record, SqlTable* table, SqlDriver* driver);

    //----------------------------------------------------------------------
    // Value Management
    //----------------------------------------------------------------------

    /**
     * @brief Set a column value
     * @param columnName The column name
     * @param value The value to set
     * @return Reference to this row for method chaining
     */
    SqlRow& setValue(std::string_view columnName, const Variant& value);

    /**
     * @brief Get a column value
     * @param columnName The column name
     * @return The column value, or empty Variant if not found
     */
    [[nodiscard]] Variant getValue(std::string_view columnName) const;

    /**
     * @brief Check if row has a value for the specified column
     * @param columnName The column name
     * @return True if value exists, false otherwise
     */
    [[nodiscard]] bool hasValue(std::string_view columnName) const noexcept;

    /**
     * @brief Remove a column value
     * @param columnName The column name
     * @return Reference to this row for method chaining
     */
    SqlRow& removeValue(std::string_view columnName);

    /**
     * @brief Get all column values
     * @return Map of column name-value pairs
     */
    [[nodiscard]] const std::unordered_map<std::string, Variant>& values() const noexcept;

    /**
     * @brief Set all column values
     * @param values The column name-value pairs
     * @return Reference to this row for method chaining
     */
    SqlRow& setValues(const std::unordered_map<std::string, Variant>& values);

    /**
     * @brief Clear all values
     * @return Reference to this row for method chaining
     */
    SqlRow& clearValues();

    //----------------------------------------------------------------------
    // Fluent Interface for Value Setting
    //----------------------------------------------------------------------

    /**
     * @brief Set a column value (fluent interface)
     * @param columnName The column name
     * @param value The value to set
     * @return Reference to this row for method chaining
     */
    template<typename T>
    SqlRow& set(std::string_view columnName, T&& value) {
        return setValue(columnName, Variant(std::forward<T>(value)));
    }

    /**
     * @brief Get a column value with type conversion
     * @tparam T The target type
     * @param columnName The column name
     * @return The converted value
     */
    template<typename T>
    [[nodiscard]] T get(std::string_view columnName) const {
        return getValue(columnName).to<T>();
    }

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------

    /**
     * @brief Get the associated table
     * @return Pointer to the table, or nullptr if not set
     */
    [[nodiscard]] SqlTable* table() const noexcept;

    /**
     * @brief Set the associated table
     * @param table The table to associate with
     */
    void setTable(SqlTable* table);

    /**
     * @brief Get the table name
     * @return The table name
     */
    [[nodiscard]] std::string tableName() const;

    //----------------------------------------------------------------------
    // Row State Management
    //----------------------------------------------------------------------

    /**
     * @brief Get the row state
     * @return The current row state
     */
    [[nodiscard]] RowState rowState() const noexcept;

    /**
     * @brief Check if row is new (not in database)
     * @return True if row is new, false otherwise
     */
    [[nodiscard]] bool isNew() const noexcept;

    /**
     * @brief Check if row is clean (no local changes)
     * @return True if row is clean, false otherwise
     */
    [[nodiscard]] bool isClean() const noexcept;

    /**
     * @brief Check if row is modified
     * @return True if row has local changes, false otherwise
     */
    [[nodiscard]] bool isModified() const noexcept;

    /**
     * @brief Check if row is marked for deletion
     * @return True if row is deleted, false otherwise
     */
    [[nodiscard]] bool isDeleted() const noexcept;

    /**
     * @brief Mark row for deletion
     * @return Reference to this row for method chaining
     */
    SqlRow& markDeleted();

    /**
     * @brief Restore row from deleted state
     * @return Reference to this row for method chaining
     */
    SqlRow& unmarkDeleted();

    //----------------------------------------------------------------------
    // Primary Key Management
    //----------------------------------------------------------------------

    /**
     * @brief Get primary key values
     * @return Map of primary key column name-value pairs
     */
    [[nodiscard]] std::unordered_map<std::string, Variant> primaryKeyValues() const;

    /**
     * @brief Set primary key values
     * @param pkValues The primary key values
     * @return Reference to this row for method chaining
     */
    SqlRow& setPrimaryKeyValues(const std::unordered_map<std::string, Variant>& pkValues);

    //----------------------------------------------------------------------
    // Database Operations
    //----------------------------------------------------------------------

    /**
     * @brief Insert row into database (Definition Mode)
     * @return True if successful, false otherwise
     */
    bool insert();

    /**
     * @brief Update row in database (Access Mode)
     * @return True if successful, false otherwise
     */
    bool update();

    /**
     * @brief Delete row from database
     * @return True if successful, false otherwise
     */
    bool deleteFromDatabase();

    /**
     * @brief Save row to database (insert if new, update if modified)
     * @return True if successful, false otherwise
     */
    bool save();

    /**
     * @brief Reload row data from database
     * @return True if successful, false otherwise
     */
    bool reload();

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------

    /**
     * @brief Get the object type
     * @return SqlObjectType::Table (rows are part of tables)
     */
    [[nodiscard]] SqlObjectType objectType() const override;

    /**
     * @brief Check if row exists in database
     * @return True if row exists (based on primary key)
     */
    [[nodiscard]] bool existsInDatabase() const override;

    /**
     * @brief Load row data from database
     * @return True if successful, false otherwise
     */
    bool loadFromDatabase() override;

    /**
     * @brief Generate INSERT SQL for row
     * @return The INSERT SQL statement
     */
    [[nodiscard]] std::string generateCreateSql() const override;

    /**
     * @brief Generate UPDATE SQL for row modifications
     * @return The UPDATE SQL statement
     */
    [[nodiscard]] std::string generateAlterSql() const override;

    //----------------------------------------------------------------------
    // Utility Methods
    //----------------------------------------------------------------------

    /**
     * @brief Get column count
     * @return Number of columns with values
     */
    [[nodiscard]] size_t columnCount() const noexcept;

    /**
     * @brief Check if row is empty (no values)
     * @return True if row has no values, false otherwise
     */
    [[nodiscard]] bool isEmpty() const noexcept;

    /**
     * @brief Convert row to SQL record
     * @return SqlRecord containing the row data
     */
    [[nodiscard]] class SqlRecord toRecord() const;

private:
    // Row data
    std::unordered_map<std::string, Variant> m_values;
    std::unordered_map<std::string, Variant> m_originalValues; // For change tracking
    std::unordered_map<std::string, Variant> m_primaryKeyValues;

    // Table association
    SqlTable* m_table{nullptr};

    // Row state
    RowState m_rowState{RowState::New};

    // Helper methods
    void setRowState(RowState state);
    std::string generateInsertSql() const;
    std::string generateUpdateSql() const;
    std::string generateDeleteSql() const;
    std::string generateWherePrimaryKey() const;
    void updateOriginalValues();
};

// Type aliases for compatibility with existing code
using Row = SqlRow;

} // namespace database

#endif // DATABASE_SQL_ROW_H
