#ifndef DATABASE_SQL_INDEX_H
#define DATABASE_SQL_INDEX_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <optional>

#include "sql_object.h"
#include "../sql_enums.h"

namespace database {

// Forward declarations
class SqlTable;
class SqlColumn;

/**
 * @brief SQL Index abstraction with dual-purpose design
 *
 * This class supports two modes of operation:
 * 1. Definition Mode: Building index definitions for new indexes
 * 2. Access Mode: Accessing existing index metadata from database
 *
 * The class provides a fluent interface for index definition and
 * automatic synchronization with database metadata.
 */
class SqlIndex : public SqlObject {
public:
    /**
     * @brief Index metadata structure
     */
    struct Metadata {
        std::string name;
        std::string tableName;
        std::string definition;
        std::vector<std::string> columnNames;
        bool unique{false};
        bool primary{false};
        bool clustered{false};

        Metadata() = default;
        explicit Metadata(std::string_view indexName) : name(indexName) {}
    };

    /**
     * @brief Index column information
     */
    struct IndexColumn {
        std::string columnName;
        SqlSortOrder sortOrder{SqlSortOrder::Ascending};
        int length{-1}; // For partial indexes

        IndexColumn(std::string_view name, SqlSortOrder order = SqlSortOrder::Ascending)
            : columnName(name), sortOrder(order) {}
    };

    //----------------------------------------------------------------------
    // Constructors (Definition Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Default constructor (Definition Mode)
     */
    SqlIndex();

    /**
     * @brief Constructor with name (Definition Mode)
     * @param name The index name
     */
    explicit SqlIndex(std::string_view name);

    /**
     * @brief Constructor with name and table (Definition Mode)
     * @param name The index name
     * @param table The associated table
     */
    SqlIndex(std::string_view name, SqlTable* table);

    /**
     * @brief Constructor for Access Mode
     * @param name The index name
     * @param table The associated table
     * @param driver The database driver
     */
    SqlIndex(std::string_view name, SqlTable* table, SqlDriver* driver);

    //----------------------------------------------------------------------
    // Static Factory Methods (Access Mode)
    //----------------------------------------------------------------------

    /**
     * @brief Create index from database metadata
     * @param name The index name
     * @param table The associated table
     * @param driver The database driver
     * @return SqlIndex instance loaded from database
     */
    [[nodiscard]] static SqlIndex fromDatabase(std::string_view name, SqlTable* table, SqlDriver* driver);

    /**
     * @brief Create index from metadata
     * @param metadata The index metadata
     * @param table The associated table
     * @param driver The database driver
     * @return SqlIndex instance
     */
    [[nodiscard]] static SqlIndex fromMetadata(const Metadata& metadata, SqlTable* table, SqlDriver* driver);

    /**
     * @brief Get all indexes for a table
     * @param table The table
     * @param driver The database driver
     * @return Vector of SqlIndex instances
     */
    [[nodiscard]] static std::vector<SqlIndex> getAllIndexes(SqlTable* table, SqlDriver* driver);

    //----------------------------------------------------------------------
    // Index Properties
    //----------------------------------------------------------------------

    /**
     * @brief Check if index is unique
     * @return True if unique, false otherwise
     */
    [[nodiscard]] bool isUnique() const noexcept;

    /**
     * @brief Set index as unique
     * @param unique Whether the index is unique
     * @return Reference to this index for method chaining
     */
    SqlIndex& setUnique(bool unique);

    /**
     * @brief Check if index is primary key
     * @return True if primary key, false otherwise
     */
    [[nodiscard]] bool isPrimary() const noexcept;

    /**
     * @brief Set index as primary key
     * @param primary Whether the index is primary key
     * @return Reference to this index for method chaining
     */
    SqlIndex& setPrimary(bool primary);

    /**
     * @brief Check if index is clustered
     * @return True if clustered, false otherwise
     */
    [[nodiscard]] bool isClustered() const noexcept;

    /**
     * @brief Set index as clustered
     * @param clustered Whether the index is clustered
     * @return Reference to this index for method chaining
     */
    SqlIndex& setClustered(bool clustered);

    //----------------------------------------------------------------------
    // Column Management
    //----------------------------------------------------------------------

    /**
     * @brief Add a column to the index
     * @param columnName The column name
     * @param sortOrder The sort order (optional)
     * @return Reference to this index for method chaining
     */
    SqlIndex& addColumn(std::string_view columnName, SqlSortOrder sortOrder = SqlSortOrder::Ascending);

    /**
     * @brief Add a column with length specification
     * @param columnName The column name
     * @param length The column length for partial indexes
     * @param sortOrder The sort order (optional)
     * @return Reference to this index for method chaining
     */
    SqlIndex& addColumn(std::string_view columnName, int length, SqlSortOrder sortOrder = SqlSortOrder::Ascending);

    /**
     * @brief Add an index column
     * @param indexColumn The index column specification
     * @return Reference to this index for method chaining
     */
    SqlIndex& addColumn(const IndexColumn& indexColumn);

    /**
     * @brief Remove a column from the index
     * @param columnName The column name
     * @return Reference to this index for method chaining
     */
    SqlIndex& removeColumn(std::string_view columnName);

    /**
     * @brief Get all index columns
     * @return Vector of index columns
     */
    [[nodiscard]] const std::vector<IndexColumn>& columns() const noexcept;

    /**
     * @brief Get column count
     * @return Number of columns in the index
     */
    [[nodiscard]] size_t columnCount() const noexcept;

    /**
     * @brief Check if index has a column
     * @param columnName The column name
     * @return True if column exists in index, false otherwise
     */
    [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept;

    /**
     * @brief Get column names
     * @return Vector of column names
     */
    [[nodiscard]] std::vector<std::string> columnNames() const;

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------

    /**
     * @brief Get the associated table
     * @return Pointer to the table, or nullptr if not set
     */
    [[nodiscard]] SqlTable* table() const noexcept;

    /**
     * @brief Set the associated table
     * @param table The table to associate with
     */
    void setTable(SqlTable* table);

    /**
     * @brief Get the table name
     * @return The table name
     */
    [[nodiscard]] std::string tableName() const;

    /**
     * @brief Set the table name
     * @param tableName The table name
     */
    void setTableName(std::string_view tableName);

    //----------------------------------------------------------------------
    // Index Metadata
    //----------------------------------------------------------------------

    /**
     * @brief Get index metadata
     * @return The index metadata
     */
    [[nodiscard]] const Metadata& metadata() const noexcept;

    /**
     * @brief Set index metadata
     * @param metadata The index metadata
     * @return Reference to this index for method chaining
     */
    SqlIndex& setMetadata(const Metadata& metadata);

    //----------------------------------------------------------------------
    // Fluent Interface for Definition Mode
    //----------------------------------------------------------------------

    /**
     * @brief Set index as unique
     * @return Reference to this index for method chaining
     */
    SqlIndex& unique();

    /**
     * @brief Set index as primary key
     * @return Reference to this index for method chaining
     */
    SqlIndex& primary();

    /**
     * @brief Set index as clustered
     * @return Reference to this index for method chaining
     */
    SqlIndex& clustered();

    /**
     * @brief Add column in ascending order
     * @param columnName The column name
     * @return Reference to this index for method chaining
     */
    SqlIndex& asc(std::string_view columnName);

    /**
     * @brief Add column in descending order
     * @param columnName The column name
     * @return Reference to this index for method chaining
     */
    SqlIndex& desc(std::string_view columnName);

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------

    /**
     * @brief Get the object type
     * @return SqlObjectType::Index
     */
    [[nodiscard]] SqlObjectType objectType() const override;

    /**
     * @brief Check if index exists in database
     * @return True if index exists
     */
    [[nodiscard]] bool existsInDatabase() const override;

    /**
     * @brief Load index definition from database
     * @return True if successful, false otherwise
     */
    bool loadFromDatabase() override;

    /**
     * @brief Generate CREATE INDEX SQL
     * @return The CREATE INDEX SQL statement
     */
    [[nodiscard]] std::string generateCreateSql() const override;

    /**
     * @brief Generate DROP INDEX SQL
     * @return The DROP INDEX SQL statement
     */
    [[nodiscard]] std::string generateDropSql() const override;

    //----------------------------------------------------------------------
    // Utility Methods
    //----------------------------------------------------------------------

    /**
     * @brief Get the qualified index name (schema.index)
     * @return The qualified index name
     */
    [[nodiscard]] std::string qualifiedIndexName() const;

    /**
     * @brief Check if index is empty (no columns)
     * @return True if index has no columns, false otherwise
     */
    [[nodiscard]] bool isEmpty() const noexcept;

private:
    // Index properties
    std::vector<IndexColumn> m_columns;
    Metadata m_metadata;

    // Table association
    SqlTable* m_table{nullptr};
    std::string m_tableName;

    // Helper methods
    void loadMetadataFromDatabase();
    std::string generateColumnList() const;
};

} // namespace database

#endif // DATABASE_SQL_INDEX_H
