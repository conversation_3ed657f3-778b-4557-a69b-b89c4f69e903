#include "sql_table.h"

#include <format>
#include <algorithm>
#include <sstream>

#include "sql_row.h"
#include "sql_index.h"
#include "../driver/sql_driver.h"
#include "../driver/sql_statement.h"

namespace database {

SqlTable::SqlTable() : SqlObject() {
}

SqlTable::SqlTable(std::string_view name) : SqlObject(name) {
}

SqlTable::SqlTable(std::string_view name, SqlDriver* driver) : SqlObject(name, driver) {
}

SqlTable SqlTable::fromDatabase(std::string_view name, SqlDriver* driver, std::string_view schema) {
    SqlTable table(name, driver);
    if (!schema.empty()) {
        table.setSchema(schema);
    }
    table.loadFromDatabase();
    return table;
}

std::vector<SqlTable> SqlTable::getAllTables(SqlDriver* driver, std::string_view schema, SqlObjectType type) {
    std::vector<SqlTable> tables;

    if (!driver) {
        return tables;
    }

    try {
        auto driverTables = driver->getTables(schema, type);
        tables.reserve(driverTables.size());

        for (const auto& driverTable : driverTables) {
            tables.push_back(driverTable);
        }
    } catch (...) {
        // Handle exceptions gracefully
    }

    return tables;
}

SqlTable& SqlTable::addColumn(const SqlColumn& column) {
    // Check if column already exists
    if (hasColumn(column.name())) {
        removeColumn(column.name());
    }

    SqlColumn newColumn = column;
    newColumn.setTable(this);
    m_columns.push_back(std::move(newColumn));
    rebuildColumnMap();
    markAsModified();

    return *this;
}

SqlTable& SqlTable::addColumn(std::string_view name, FieldType type) {
    SqlColumn column(name, type);
    return addColumn(column);
}

SqlTable& SqlTable::addColumn(std::string_view name, FieldType type, int size) {
    SqlColumn column(name, type, size);
    return addColumn(column);
}

SqlTable& SqlTable::removeColumn(std::string_view name) {
    auto it = std::remove_if(m_columns.begin(), m_columns.end(),
        [name](const SqlColumn& col) { return col.name() == name; });

    if (it != m_columns.end()) {
        m_columns.erase(it, m_columns.end());
        rebuildColumnMap();
        markAsModified();
    }

    return *this;
}

SqlColumn* SqlTable::getColumn(std::string_view name) {
    auto it = m_columnMap.find(std::string(name));
    if (it != m_columnMap.end() && it->second < m_columns.size()) {
        return &m_columns[it->second];
    }
    return nullptr;
}

const SqlColumn* SqlTable::getColumn(std::string_view name) const {
    auto it = m_columnMap.find(std::string(name));
    if (it != m_columnMap.end() && it->second < m_columns.size()) {
        return &m_columns[it->second];
    }
    return nullptr;
}

const std::vector<SqlColumn>& SqlTable::columns() const noexcept {
    return m_columns;
}

size_t SqlTable::columnCount() const noexcept {
    return m_columns.size();
}

bool SqlTable::hasColumn(std::string_view name) const noexcept {
    return m_columnMap.find(std::string(name)) != m_columnMap.end();
}

SqlTable& SqlTable::setPrimaryKey(const std::vector<std::string>& columnNames) {
    // Validate that all columns exist
    for (const auto& colName : columnNames) {
        if (!hasColumn(colName)) {
            setError(std::format("Column '{}' not found for primary key", colName), ErrorCode::InvalidArgument);
            return *this;
        }
    }

    m_primaryKeyColumns = columnNames;

    // Mark primary key columns
    for (const auto& colName : columnNames) {
        if (auto* col = getColumn(colName)) {
            col->primaryKey();
        }
    }

    markAsModified();
    return *this;
}

SqlTable& SqlTable::setPrimaryKey(std::string_view columnName) {
    return setPrimaryKey(std::vector<std::string>{std::string(columnName)});
}

const std::vector<std::string>& SqlTable::primaryKeyColumns() const noexcept {
    return m_primaryKeyColumns;
}

bool SqlTable::hasPrimaryKey() const noexcept {
    return !m_primaryKeyColumns.empty();
}

const SqlTable::Metadata& SqlTable::metadata() const noexcept {
    return m_metadata;
}

SqlTable& SqlTable::setMetadata(const Metadata& metadata) {
    m_metadata = metadata;
    markAsModified();
    return *this;
}

SqlTable& SqlTable::setEngine(std::string_view engine) {
    m_metadata.engine = engine;
    markAsModified();
    return *this;
}

SqlTable& SqlTable::setCharset(std::string_view charset) {
    m_metadata.charset = charset;
    markAsModified();
    return *this;
}

SqlTable& SqlTable::setCollation(std::string_view collation) {
    m_metadata.collation = collation;
    markAsModified();
    return *this;
}

SqlTable& SqlTable::setComment(std::string_view comment) {
    m_metadata.comment = comment;
    markAsModified();
    return *this;
}

SqlTable& SqlTable::setTemporary(bool temporary) {
    m_metadata.temporary = temporary;
    markAsModified();
    return *this;
}

int64_t SqlTable::rowCount() const {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return -1;
    }

    std::string sql = std::format("SELECT COUNT(*) FROM {}", qualifiedName());
    auto stmt = prepareStatement(sql);
    if (!stmt) {
        return -1;
    }

    if (!stmt->execute()) {
        setError("Failed to execute row count query", ErrorCode::ExecutionFailed);
        return -1;
    }

    if (stmt->step()) {
        return stmt->value(0).toLong();
    }

    return -1;
}

bool SqlTable::isEmpty() const {
    return rowCount() == 0;
}

bool SqlTable::truncate() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    std::string sql = std::format("DELETE FROM {}", qualifiedName());
    return executeSql(sql);
}

bool SqlTable::insertRow(const SqlRow& row) {
    return insertRows({row});
}

bool SqlTable::insertRows(const std::vector<SqlRow>& rows) {
    if (rows.empty()) {
        return true;
    }

    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    // Generate INSERT SQL
    std::ostringstream sql;
    sql << "INSERT INTO " << qualifiedName() << " (";

    // Add column names
    bool first = true;
    for (const auto& column : m_columns) {
        if (!first) sql << ", ";
        sql << column.name();
        first = false;
    }

    sql << ") VALUES ";

    // Add values for each row
    first = true;
    for (const auto& row : rows) {
        if (!first) sql << ", ";
        sql << "(";

        bool firstCol = true;
        for (const auto& column : m_columns) {
            if (!firstCol) sql << ", ";
            sql << "?"; // Use parameter placeholders
            firstCol = false;
        }

        sql << ")";
        first = false;
    }

    auto stmt = prepareStatement(sql.str());
    if (!stmt) {
        return false;
    }

    // Bind parameters
    int paramIndex = 1;
    for (const auto& row : rows) {
        for (const auto& column : m_columns) {
            Variant value = row.getValue(column.name());
            stmt->bindValue(paramIndex++, value);
        }
    }

    return stmt->execute();
}

std::vector<SqlIndex> SqlTable::getIndexes() const {
    std::vector<SqlIndex> indexes;

    if (!driver() || !driver()->isConnected()) {
        return indexes;
    }

    try {
        // Use driver's method to get indexes for this table
        auto indexMetadata = driver()->getIndexes(*this);
        indexes.reserve(indexMetadata.size());

        for (const auto& metadata : indexMetadata) {
            SqlIndex index(metadata.name, const_cast<SqlTable*>(this), driver());
            indexes.push_back(std::move(index));
        }
    } catch (...) {
        // Handle exceptions gracefully
    }

    return indexes;
}

bool SqlTable::createIndex(std::string_view indexName, const std::vector<std::string>& columnNames, bool unique) {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (columnNames.empty()) {
        setError("No columns specified for index", ErrorCode::InvalidArgument);
        return false;
    }

    // Validate that all columns exist
    for (const auto& colName : columnNames) {
        if (!hasColumn(colName)) {
            setError(std::format("Column '{}' not found for index", colName), ErrorCode::InvalidArgument);
            return false;
        }
    }

    // Generate CREATE INDEX SQL
    std::ostringstream sql;
    sql << "CREATE ";
    if (unique) {
        sql << "UNIQUE ";
    }
    sql << "INDEX " << indexName << " ON " << qualifiedName() << " (";

    bool first = true;
    for (const auto& colName : columnNames) {
        if (!first) sql << ", ";
        sql << colName;
        first = false;
    }
    sql << ")";

    return executeSql(sql.str());
}

bool SqlTable::dropIndex(std::string_view indexName) {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    std::string sql = std::format("DROP INDEX {}", indexName);
    return executeSql(sql);
}

SqlObjectType SqlTable::objectType() const {
    return SqlObjectType::Table;
}

bool SqlTable::existsInDatabase() const {
    if (!driver()) {
        return false;
    }

    return driver()->tableExists(*this);
}

bool SqlTable::loadFromDatabase() {
    if (!driver() || !driver()->isConnected()) {
        setError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    if (!existsInDatabase()) {
        setError("Table does not exist in database", ErrorCode::ExecutionFailed);
        return false;
    }

    loadColumnsFromDatabase();
    loadMetadataFromDatabase();

    setSyncState(SqlSyncState::Synced);
    clearModified();
    clearError();

    return true;
}

std::string SqlTable::generateCreateSql() const {
    if (m_columns.empty()) {
        return "";
    }

    std::ostringstream sql;

    // CREATE TABLE clause
    sql << "CREATE ";
    if (m_metadata.temporary) {
        sql << "TEMPORARY ";
    }
    sql << "TABLE " << qualifiedName() << " (\n";

    // Add columns
    bool first = true;
    for (const auto& column : m_columns) {
        if (!first) sql << ",\n";
        sql << "  " << column.generateCreateSql();
        first = false;
    }

    // Add primary key constraint
    if (hasPrimaryKey()) {
        sql << ",\n  " << generatePrimaryKeyConstraint();
    }

    sql << "\n)";

    // Add table options
    std::string options = generateTableOptions();
    if (!options.empty()) {
        sql << " " << options;
    }

    return sql.str();
}

std::string SqlTable::generateAlterSql() const {
    // This is a simplified implementation
    // In practice, this would generate specific ALTER statements for changes
    return std::format("-- ALTER TABLE {} statements would go here", qualifiedName());
}

std::string SqlTable::generateDropSql() const {
    return std::format("DROP TABLE {}", qualifiedName());
}

void SqlTable::rebuildColumnMap() {
    m_columnMap.clear();
    for (size_t i = 0; i < m_columns.size(); ++i) {
        m_columnMap[std::string(m_columns[i].name())] = i;
    }
}

void SqlTable::loadColumnsFromDatabase() {
    if (!driver()) {
        return;
    }

    // This would typically query the database schema tables
    // For now, this is a placeholder implementation
    // Database-specific implementations would override this

    m_columns.clear();
    m_columnMap.clear();

    // Example: Load columns using driver's metadata methods
    // auto columnMetadata = driver()->getTableColumns(*this);
    // for (const auto& metadata : columnMetadata) {
    //     SqlColumn column = SqlColumn::fromField(metadata, this, driver());
    //     m_columns.push_back(std::move(column));
    // }

    rebuildColumnMap();
}

void SqlTable::loadMetadataFromDatabase() {
    if (!driver()) {
        return;
    }

    // This would typically query the database schema tables
    // For now, this is a placeholder implementation
    // Database-specific implementations would override this

    // Example: Load table metadata
    // auto metadata = driver()->getTableMetadata(*this);
    // m_metadata = metadata;
}

std::string SqlTable::generatePrimaryKeyConstraint() const {
    if (m_primaryKeyColumns.empty()) {
        return "";
    }

    std::ostringstream sql;
    sql << "PRIMARY KEY (";

    bool first = true;
    for (const auto& colName : m_primaryKeyColumns) {
        if (!first) sql << ", ";
        sql << colName;
        first = false;
    }

    sql << ")";
    return sql.str();
}

std::string SqlTable::generateTableOptions() const {
    std::ostringstream sql;

    if (!m_metadata.engine.empty()) {
        sql << "ENGINE=" << m_metadata.engine;
    }

    if (!m_metadata.charset.empty()) {
        if (!sql.str().empty()) sql << " ";
        sql << "DEFAULT CHARSET=" << m_metadata.charset;
    }

    if (!m_metadata.collation.empty()) {
        if (!sql.str().empty()) sql << " ";
        sql << "COLLATE=" << m_metadata.collation;
    }

    if (!m_metadata.comment.empty()) {
        if (!sql.str().empty()) sql << " ";
        sql << "COMMENT='" << m_metadata.comment << "'";
    }

    if (m_metadata.autoIncrementValue > 1) {
        if (!sql.str().empty()) sql << " ";
        sql << "AUTO_INCREMENT=" << m_metadata.autoIncrementValue;
    }

    return sql.str();
}

} // namespace database
